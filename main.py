#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理主程序
功能：
1. 加载和合并PAI集群数据
2. 构建基于时间序列的cpu_usage, gpu_wrk_util, avg_mem数据表
3. 生成clean.csv数据集
4. 进行数据可视化和统计分析
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 添加analysis目录到路径
sys.path.append('./analysis')
from utils import load_all_df, get_df

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_and_merge_data():
    """
    加载并合并所有数据表
    """
    print("正在加载数据表...")
    try:
        # 使用utils.py中的函数加载所有数据表
        dfj, dft, dfi, dfs, dfg, dfp, dfm = load_all_df()
        print(f"成功加载数据表:")
        print(f"- pai_job_table: {dfj.shape}")
        print(f"- pai_task_table: {dft.shape}")
        print(f"- pai_instance_table: {dfi.shape}")
        print(f"- pai_sensor_table: {dfs.shape}")
        print(f"- pai_group_tag_table: {dfg.shape}")
        print(f"- pai_machine_spec: {dfp.shape}")
        print(f"- pai_machine_metric: {dfm.shape}")

        return dfj, dft, dfi, dfs, dfg, dfp, dfm
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None

def create_time_series_dataset(dfs, dfi):
    """
    创建基于时间序列的数据集，包含cpu_usage, gpu_wrk_util, avg_mem
    """
    print("\n正在创建时间序列数据集...")

    # 合并sensor表和instance表以获取时间信息
    print("合并sensor表和instance表...")
    df_merged = dfs.merge(dfi[['worker_name', 'start_time', 'end_time']],
                         on='worker_name', how='left')

    # 过滤掉无效的时间数据
    df_merged = df_merged[(df_merged['start_time'] > 0) & (df_merged['end_time'] > 0)]

    # 选择需要的列
    columns_needed = ['worker_name', 'job_name', 'task_name', 'machine',
                     'start_time', 'end_time', 'cpu_usage', 'gpu_wrk_util', 'avg_mem']

    df_clean = df_merged[columns_needed].copy()

    # 计算运行时长
    df_clean['duration'] = df_clean['end_time'] - df_clean['start_time']

    # 转换时间戳为日期时间
    df_clean['start_datetime'] = pd.to_datetime(df_clean['start_time'], unit='s')
    df_clean['end_datetime'] = pd.to_datetime(df_clean['end_time'], unit='s')

    # 移除异常值
    print("移除异常值...")
    # 移除CPU使用率异常值（负值或过大值）
    df_clean = df_clean[df_clean['cpu_usage'] >= 0]
    df_clean = df_clean[df_clean['cpu_usage'] <= 10000]  # 100个CPU核心上限

    # 移除GPU使用率异常值
    df_clean = df_clean[df_clean['gpu_wrk_util'] >= 0]
    df_clean = df_clean[df_clean['gpu_wrk_util'] <= 100]

    # 移除内存使用异常值
    df_clean = df_clean[df_clean['avg_mem'] >= 0]
    df_clean = df_clean[df_clean['avg_mem'] <= 1000]  # 1TB内存上限

    # 移除运行时长异常值
    df_clean = df_clean[df_clean['duration'] > 0]
    df_clean = df_clean[df_clean['duration'] <= 7*24*3600]  # 7天上限

    print(f"清洗后的数据集大小: {df_clean.shape}")

    return df_clean

def save_clean_dataset(df_clean, filename='clean.csv'):
    """
    保存清洁的数据集
    """
    print(f"\n正在保存数据集到 {filename}...")

    # 按时间排序
    df_clean_sorted = df_clean.sort_values('start_time').reset_index(drop=True)

    # 保存到CSV文件
    df_clean_sorted.to_csv(filename, index=False)
    print(f"数据集已保存到 {filename}")
    print(f"数据集包含 {len(df_clean_sorted)} 条记录")

    return df_clean_sorted

def calculate_basic_statistics(df):
    """
    计算基本统计量
    """
    print("\n=== 基本统计量分析 ===")

    metrics = ['cpu_usage', 'gpu_wrk_util', 'avg_mem', 'duration']
    stats_dict = {}

    for metric in metrics:
        data = df[metric].dropna()
        stats = {
            '计数': len(data),
            '均值': data.mean(),
            '中位数': data.median(),
            '标准差': data.std(),
            '方差': data.var(),
            '最小值': data.min(),
            '最大值': data.max(),
            '25%分位数': data.quantile(0.25),
            '75%分位数': data.quantile(0.75)
        }
        stats_dict[metric] = stats

        print(f"\n{metric} 统计量:")
        for stat_name, stat_value in stats.items():
            if stat_name == '计数':
                print(f"  {stat_name}: {stat_value:,}")
            else:
                print(f"  {stat_name}: {stat_value:.4f}")

    # 创建统计量DataFrame
    stats_df = pd.DataFrame(stats_dict).T
    stats_df.to_csv('statistics_summary.csv')
    print(f"\n统计量摘要已保存到 statistics_summary.csv")

    return stats_dict

def create_visualizations(df):
    """
    创建数据可视化图表
    """
    print("\n正在生成可视化图表...")

    # 创建figures目录
    if not os.path.exists('figures'):
        os.makedirs('figures')

    # 设置图表样式
    plt.style.use('default')
    sns.set_palette("husl")

    # 1. 时间序列分布图
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('PAI集群资源使用情况分析', fontsize=16, fontweight='bold')

    # CPU使用率分布
    axes[0, 0].hist(df['cpu_usage'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('CPU使用率分布')
    axes[0, 0].set_xlabel('CPU使用率 (%)')
    axes[0, 0].set_ylabel('频次')
    axes[0, 0].grid(True, alpha=0.3)

    # GPU使用率分布
    axes[0, 1].hist(df['gpu_wrk_util'], bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[0, 1].set_title('GPU使用率分布')
    axes[0, 1].set_xlabel('GPU使用率 (%)')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].grid(True, alpha=0.3)

    # 内存使用分布
    axes[1, 0].hist(df['avg_mem'], bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[1, 0].set_title('平均内存使用分布')
    axes[1, 0].set_xlabel('平均内存使用 (GB)')
    axes[1, 0].set_ylabel('频次')
    axes[1, 0].grid(True, alpha=0.3)

    # 运行时长分布
    axes[1, 1].hist(df['duration']/3600, bins=50, alpha=0.7, color='gold', edgecolor='black')
    axes[1, 1].set_title('任务运行时长分布')
    axes[1, 1].set_xlabel('运行时长 (小时)')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('figures/resource_usage_distributions.png', dpi=300, bbox_inches='tight')
    plt.show()

    return True

def main():
    """
    主函数
    """
    print("=== PAI集群数据预处理程序 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 1. 加载数据
    data_tables = load_and_merge_data()
    if data_tables is None:
        print("数据加载失败，程序退出")
        return

    dfj, dft, dfi, dfs, dfg, dfp, dfm = data_tables

    # 2. 创建时间序列数据集
    df_clean = create_time_series_dataset(dfs, dfi)

    # 3. 保存清洁数据集
    df_final = save_clean_dataset(df_clean)

    # 4. 计算基本统计量
    stats = calculate_basic_statistics(df_final)

    # 5. 生成可视化图表
    create_visualizations(df_final)

    print(f"\n=== 数据预处理完成 ===")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"生成的文件:")
    print(f"- clean.csv: 清洁的时间序列数据集")
    print(f"- statistics_summary.csv: 统计量摘要")
    print(f"- figures/resource_usage_distributions.png: 资源使用分布图")

if __name__ == "__main__":
    main()
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理主程序
功能：
1. 加载和合并PAI集群数据
2. 构建基于时间序列的cpu_usage, gpu_wrk_util, avg_mem数据表
3. 生成clean.csv数据集
4. 进行数据可视化和统计分析
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')



def load_and_merge_data():
    """
    加载并合并所有数据表
    """
    print("正在加载数据表...")
    try:
        # 直接使用正确的路径加载数据表
        data_dir = './data/'

        def get_df_local(file, header=None):
            df = pd.read_csv(file, header=None)
            header_file = file.replace('.csv', '.header')
            df.columns = pd.read_csv(header_file).columns if header is None else header
            return df

        dfj = get_df_local(data_dir + 'pai_job_table.csv')
        dft = get_df_local(data_dir + 'pai_task_table.csv')
        dfi = get_df_local(data_dir + 'pai_instance_table.csv')
        dfs = get_df_local(data_dir + 'pai_sensor_table.csv')
        dfg = get_df_local(data_dir + 'pai_group_tag_table.csv')
        dfp = get_df_local(data_dir + 'pai_machine_spec.csv')
        dfm = get_df_local(data_dir + 'pai_machine_metric.csv')

        print(f"成功加载数据表:")
        print(f"- pai_job_table: {dfj.shape}")
        print(f"- pai_task_table: {dft.shape}")
        print(f"- pai_instance_table: {dfi.shape}")
        print(f"- pai_sensor_table: {dfs.shape}")
        print(f"- pai_group_tag_table: {dfg.shape}")
        print(f"- pai_machine_spec: {dfp.shape}")
        print(f"- pai_machine_metric: {dfm.shape}")

        return dfj, dft, dfi, dfs, dfg, dfp, dfm
    except Exception as e:
        print(f"加载数据时出错: {e}")
        return None

def create_time_series_dataset(dfs, dfi):
    """
    创建基于时间序列的数据集，包含date, hour, cpu_usage, gpu_wrk_util, avg_mem
    """
    print("\n正在创建时间序列数据集...")

    # 合并sensor表和instance表以获取时间信息
    print("合并sensor表和instance表...")
    df_merged = dfs.merge(dfi[['worker_name', 'start_time', 'end_time']],
                         on='worker_name', how='left')

    # 过滤掉无效的时间数据
    df_merged = df_merged[(df_merged['start_time'] > 0) & (df_merged['end_time'] > 0)]

    # 转换时间戳为日期时间，设置时区（参考官方代码）
    df_merged['start_datetime'] = pd.to_datetime(df_merged['start_time'], unit='s', utc=True)
    df_merged['start_datetime'] = df_merged['start_datetime'].dt.tz_convert('Asia/Shanghai')

    # 参考官方代码，计算dayofyear和hourofyear
    df_merged['dayofyear'] = df_merged['start_datetime'].dt.dayofyear
    df_merged['hour'] = df_merged['start_datetime'].dt.hour
    df_merged['hourofyear'] = df_merged['dayofyear'] * 24 + df_merged['hour']

    print("处理缺失值...")
    # 检查缺失值情况
    print("缺失值统计:")
    missing_stats = df_merged[['cpu_usage', 'gpu_wrk_util', 'avg_mem']].isnull().sum()
    for col, missing_count in missing_stats.items():
        print(f"  {col}: {missing_count} ({missing_count/len(df_merged)*100:.2f}%)")

    # 处理缺失值
    # 1. 删除所有三个指标都缺失的记录
    df_clean = df_merged.dropna(subset=['cpu_usage', 'gpu_wrk_util', 'avg_mem'], how='all')

    # 2. 对于部分缺失的数据，使用前向填充和后向填充
    df_clean = df_clean.sort_values(['hourofyear'])

    # 按hourofyear分组，计算每小时的平均值来填充缺失值
    hourly_means = df_clean.groupby('hourofyear')[['cpu_usage', 'gpu_wrk_util', 'avg_mem']].transform('mean')

    # 用同一小时的平均值填充缺失值
    for col in ['cpu_usage', 'gpu_wrk_util', 'avg_mem']:
        df_clean[col] = df_clean[col].fillna(hourly_means[col])

    # 如果还有缺失值，使用全局中位数填充
    for col in ['cpu_usage', 'gpu_wrk_util', 'avg_mem']:
        if df_clean[col].isnull().sum() > 0:
            median_val = df_clean[col].median()
            df_clean[col] = df_clean[col].fillna(median_val)
            print(f"  用中位数 {median_val:.4f} 填充 {col} 的剩余缺失值")

    print("移除异常值...")
    # 移除CPU使用率异常值（负值或过大值）
    df_clean = df_clean[df_clean['cpu_usage'] >= 0]
    df_clean = df_clean[df_clean['cpu_usage'] <= 1500]  # 100个CPU核心上限

    # 移除GPU使用率异常值
    df_clean = df_clean[df_clean['gpu_wrk_util'] >= 0]
    df_clean = df_clean[df_clean['gpu_wrk_util'] <= 100]

    # 移除内存使用异常值
    df_clean = df_clean[df_clean['avg_mem'] >= 0]
    df_clean = df_clean[df_clean['avg_mem'] <= 1000]  # 1TB内存上限

    # 按hourofyear聚合数据，计算每小时的平均资源使用情况（参考官方代码）
    print("按时间聚合数据...")
    time_series_data = df_clean.groupby('hourofyear').agg({
        'cpu_usage': 'mean',
        'gpu_wrk_util': 'mean',
        'avg_mem': 'mean'
    }).reset_index()

    # 找到数据的起始点，设置offset（参考官方代码）
    min_hourofyear = time_series_data['hourofyear'].min()
    offset = min_hourofyear
    time_series_data['hour'] = time_series_data['hourofyear'] - offset

    # 只提取一周的数据（168小时）
    print("提取一周的数据（168小时）...")
    one_week_data = time_series_data[(time_series_data['hour'] >= 0) & (time_series_data['hour'] < 168)].copy()

    # 创建标准的date和hour字段
    one_week_data['date'] = one_week_data['hour'].apply(lambda x: f"Day_{x//24 + 1}")
    one_week_data['hour_of_day'] = one_week_data['hour'] % 24

    # 创建datetime字段用于可视化
    base_date = pd.Timestamp('2020-07-01')  # 使用一个基准日期
    one_week_data['datetime'] = one_week_data['hour'].apply(lambda x: base_date + pd.Timedelta(hours=x))

    print(f"一周时间序列数据集大小: {one_week_data.shape}")
    print(f"时间范围: 从第0小时到第{one_week_data['hour'].max()}小时")
    print(f"对应日期范围: {one_week_data['datetime'].min()} 到 {one_week_data['datetime'].max()}")

    return one_week_data

def save_clean_dataset(df_clean, filename='clean.csv'):
    """
    保存清洁的数据集
    """
    print(f"\n正在保存数据集到 {filename}...")

    # 按时间排序
    df_clean_sorted = df_clean.sort_values('datetime').reset_index(drop=True)

    # 保存到CSV文件
    df_clean_sorted.to_csv(filename, index=False)
    print(f"数据集已保存到 {filename}")
    print(f"数据集包含 {len(df_clean_sorted)} 条记录")
    print(f"数据集列名: {list(df_clean_sorted.columns)}")

    return df_clean_sorted

def calculate_basic_statistics(df):
    """
    计算基本统计量
    """
    print("\n=== 基本统计量分析 ===")

    metrics = ['cpu_usage', 'gpu_wrk_util', 'avg_mem']
    stats_dict = {}

    for metric in metrics:
        data = df[metric].dropna()
        stats = {
            '计数': len(data),
            '均值': data.mean(),
            '中位数': data.median(),
            '标准差': data.std(),
            '方差': data.var(),
            '最小值': data.min(),
            '最大值': data.max(),
            '25%分位数': data.quantile(0.25),
            '75%分位数': data.quantile(0.75)
        }
        stats_dict[metric] = stats

        print(f"\n{metric} 统计量:")
        for stat_name, stat_value in stats.items():
            if stat_name == '计数':
                print(f"  {stat_name}: {stat_value:,}")
            else:
                print(f"  {stat_name}: {stat_value:.4f}")

    # 创建统计量DataFrame
    stats_df = pd.DataFrame(stats_dict).T
    stats_df.to_csv('statistics_summary.csv')
    print(f"\n统计量摘要已保存到 statistics_summary.csv")

    return stats_dict

def create_time_series_visualizations(df):
    """
    创建时间序列可视化图表（参考官方代码风格）
    """
    print("\n正在生成时间序列可视化图表...")

    # 设置图表样式
    plt.style.use('default')

    # 参考官方代码的绘图方式
    plt.figure(figsize=(12, 3), dpi=120)
    num_days = 7

    # 绘制CPU、GPU、内存使用率（参考官方代码的缩放方式）
    plt.plot(df.set_index('hour')['cpu_usage']/1000, label='10 CPU cores', linestyle='solid')
    plt.plot(df.set_index('hour')['gpu_wrk_util']/100, label='1 GPU', linestyle='dotted')
    plt.plot(df.set_index('hour')['avg_mem']/100, label='100 GB Memory', linestyle='dashed')

    plt.xlabel('Hours from the beginning of a week (Sun. to Sat.)')
    plt.ylabel('Total resource requests')
    plt.grid(alpha=0.8, linestyle='-.')
    plt.legend(ncol=1, loc='best')
    plt.xlim(0, 24*num_days)
    plt.xticks([24 * x for x in range(num_days+1)])

    # 保存到当前目录
    plt.tight_layout()
    plt.savefig('time_series_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 创建分别的时间序列图
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))
    fig.suptitle('PAI集群资源使用时间序列分析（一周数据）', fontsize=16, fontweight='bold')

    # CPU使用率时间序列
    axes[0].plot(df['hour'], df['cpu_usage'], linewidth=1.5, color='#1f77b4', alpha=0.8)
    axes[0].set_title('CPU使用率时间序列', fontsize=14)
    axes[0].set_ylabel('CPU使用率', fontsize=12)
    axes[0].grid(True, alpha=0.3)
    axes[0].set_xlim(0, 168)
    axes[0].set_xticks([24 * x for x in range(8)])

    # GPU使用率时间序列
    axes[1].plot(df['hour'], df['gpu_wrk_util'], linewidth=1.5, color='#ff7f0e', alpha=0.8)
    axes[1].set_title('GPU使用率时间序列', fontsize=14)
    axes[1].set_ylabel('GPU使用率 (%)', fontsize=12)
    axes[1].grid(True, alpha=0.3)
    axes[1].set_xlim(0, 168)
    axes[1].set_xticks([24 * x for x in range(8)])

    # 内存使用时间序列
    axes[2].plot(df['hour'], df['avg_mem'], linewidth=1.5, color='#2ca02c', alpha=0.8)
    axes[2].set_title('平均内存使用时间序列', fontsize=14)
    axes[2].set_ylabel('平均内存使用 (GB)', fontsize=12)
    axes[2].set_xlabel('小时（从一周开始）', fontsize=12)
    axes[2].grid(True, alpha=0.3)
    axes[2].set_xlim(0, 168)
    axes[2].set_xticks([24 * x for x in range(8)])

    plt.tight_layout()
    plt.savefig('detailed_time_series.png', dpi=300, bbox_inches='tight')
    plt.show()

    return True

def main():
    """
    主函数
    """
    print("=== PAI集群数据预处理程序 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 1. 加载数据
    data_tables = load_and_merge_data()
    if data_tables is None:
        print("数据加载失败，程序退出")
        return

    dfj, dft, dfi, dfs, dfg, dfp, dfm = data_tables

    # 2. 创建时间序列数据集
    df_clean = create_time_series_dataset(dfs, dfi)

    # 3. 保存清洁数据集
    df_final = save_clean_dataset(df_clean)

    # 4. 计算基本统计量
    stats = calculate_basic_statistics(df_final)

    # 5. 生成时间序列可视化图表
    create_time_series_visualizations(df_final)

    print(f"\n=== 数据预处理完成 ===")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"生成的文件:")
    print(f"- clean.csv: 清洁的时间序列数据集（一周168小时数据，包含date, hour, cpu_usage, gpu_wrk_util, avg_mem）")
    print(f"- statistics_summary.csv: 统计量摘要")
    print(f"- time_series_analysis.png: 官方风格的时间序列分析图")
    print(f"- detailed_time_series.png: 详细的时间序列分析图")

if __name__ == "__main__":
    main()
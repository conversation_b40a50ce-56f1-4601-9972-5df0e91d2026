#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PAI集群时间序列预测主程序
功能：
1. 加载clean.csv数据集
2. 数据预处理和时间序列构建
3. 使用LSTM和ARIMA模型进行预测
4. 评估模型性能并生成可视化结果
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 深度学习相关
import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import LSTM, Dense, Dropout, Input, concatenate
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

# 时间序列分析
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.stattools import adfuller

# 特征工程
from scipy import stats
from scipy.signal import savgol_filter

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置随机种子
np.random.seed(42)
tf.random.set_seed(42)

def create_output_directory():
    """创建输出目录"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"output/{timestamp}"
    os.makedirs(output_dir, exist_ok=True)
    return output_dir

def load_and_preprocess_data():
    """加载并预处理数据"""
    print("正在加载数据集...")

    # 读取clean.csv
    df = pd.read_csv('clean.csv')
    print(f"原始数据集大小: {df.shape}")
    print(f"数据集列名: {list(df.columns)}")

    # 转换时间列
    if 'start_date' in df.columns:
        df['start_date'] = pd.to_datetime(df['start_date'])

    # 从1970-01-26开始记录数据（跳过前面异常的一周）
    start_date = pd.to_datetime('1970-01-26 00:00:00')
    print(f"从 {start_date} 开始提取数据...")

    # 过滤数据
    if 'start_date' in df.columns:
        df_filtered = df[df['start_date'] >= start_date].copy()
    else:
        df_filtered = df.copy()

    print(f"过滤后数据集大小: {df_filtered.shape}")

    # 确保hour列是数值类型
    if 'hour' in df_filtered.columns:
        print(f"hour列示例数据: {df_filtered['hour'].head(3).tolist()}")
        
        # 如果hour列是datetime格式，转换为小时偏移
        if df_filtered['hour'].dtype == 'object':
            try:
                # 尝试解析为datetime
                df_filtered['hour'] = pd.to_datetime(df_filtered['hour'])
                # 计算相对于start_date的小时偏移
                df_filtered['hour'] = (df_filtered['hour'] - start_date).dt.total_seconds() / 3600
                df_filtered['hour'] = df_filtered['hour'].astype(int)
                print(f"将datetime格式的hour列转换为小时偏移")
            except:
                # 如果不是datetime格式，尝试直接转换为数值
                df_filtered['hour'] = pd.to_numeric(df_filtered['hour'], errors='coerce')
                df_filtered = df_filtered.dropna(subset=['hour'])
                df_filtered['hour'] = df_filtered['hour'].astype(int)
        else:
            df_filtered['hour'] = pd.to_numeric(df_filtered['hour'], errors='coerce')
            df_filtered = df_filtered.dropna(subset=['hour'])
            df_filtered['hour'] = df_filtered['hour'].astype(int)
        
        print(f"hour列类型转换后数据集大小: {df_filtered.shape}")
        print(f"转换后hour列示例: {df_filtered['hour'].head(3).tolist()}")

    # 按小时聚合数据
    print("按小时聚合数据...")

    if 'hour' in df_filtered.columns:
        hourly_data = df_filtered.groupby('hour').agg({
            'cpu_usage': 'mean',
            'gpu_wrk_util': 'mean',
            'avg_mem': 'mean'
        }).reset_index()
    else:
        df_filtered['hour'] = (df_filtered['start_date'] - start_date).dt.total_seconds() / 3600
        df_filtered['hour'] = df_filtered['hour'].astype(int)
        hourly_data = df_filtered.groupby('hour').agg({
            'cpu_usage': 'mean',
            'gpu_wrk_util': 'mean',
            'avg_mem': 'mean'
        }).reset_index()

    # 检查是否有数据，并处理NaN值
    if hourly_data.empty:
        print("错误：聚合后的数据为空")
        return pd.DataFrame()
    
    hourly_data = hourly_data.dropna(subset=['hour'])
    
    if hourly_data.empty:
        print("错误：删除NaN后数据为空")
        return pd.DataFrame()

    # 确保数据连续性
    max_hour = int(hourly_data['hour'].max())
    min_hour = int(hourly_data['hour'].min())
    
    print(f"小时范围: {min_hour} 到 {max_hour}")
    
    # 限制数据范围，避免内存问题
    if max_hour - min_hour > 100000:
        print(f"数据跨度太大({max_hour - min_hour}小时)，只取最后10000小时")
        min_hour = max_hour - 10000
        hourly_data = hourly_data[hourly_data['hour'] >= min_hour]
    
    full_hours = pd.DataFrame({'hour': range(min_hour, max_hour + 1)})
    hourly_data = full_hours.merge(hourly_data, on='hour', how='left')

    # 填充缺失值
    hourly_data = hourly_data.fillna(method='ffill').fillna(method='bfill')
    for col in ['cpu_usage', 'gpu_wrk_util', 'avg_mem']:
        if col in hourly_data.columns:
            mean_val = hourly_data[col].mean()
            if pd.isna(mean_val):
                mean_val = 0
            hourly_data[col] = hourly_data[col].fillna(mean_val)

    # Z-score异常值检测和移除
    print("开始Z-score异常值检测...")
    original_size = len(hourly_data)
    outlier_counts = {}
    
    for col in ['cpu_usage', 'gpu_wrk_util', 'avg_mem']:
        if col in hourly_data.columns:
            # 计算Z-score
            mean_val = hourly_data[col].mean()
            std_val = hourly_data[col].std()
            
            if std_val > 0:  # 避免除零错误
                z_scores = np.abs((hourly_data[col] - mean_val) / std_val)
                # 标记异常值（|z-score| > 3）
                outliers = z_scores > 3
                outlier_count = outliers.sum()
                outlier_counts[col] = outlier_count
                
                print(f"{col}: 检测到 {outlier_count} 个异常值 (占比: {outlier_count/len(hourly_data)*100:.2f}%)")
                
                # 移除异常值
                hourly_data = hourly_data[~outliers]
    
    cleaned_size = len(hourly_data)
    removed_count = original_size - cleaned_size
    print(f"Z-score异常值检测完成:")
    print(f"  原始数据点: {original_size}")
    print(f"  移除异常值: {removed_count} (占比: {removed_count/original_size*100:.2f}%)")
    print(f"  清洁数据点: {cleaned_size}")
    
    # 确保数据连续性（重新填充由于异常值移除造成的空隙）
    if not hourly_data.empty:
        # 重新创建连续的小时序列
        min_hour = int(hourly_data['hour'].min())
        max_hour = int(hourly_data['hour'].max())
        full_hours = pd.DataFrame({'hour': range(min_hour, max_hour + 1)})
        hourly_data = full_hours.merge(hourly_data, on='hour', how='left')
        
        # 重新填充缺失值
        hourly_data = hourly_data.fillna(method='ffill').fillna(method='bfill')
        for col in ['cpu_usage', 'gpu_wrk_util', 'avg_mem']:
            if col in hourly_data.columns:
                mean_val = hourly_data[col].mean()
                if pd.isna(mean_val):
                    mean_val = 0
                hourly_data[col] = hourly_data[col].fillna(mean_val)

    print(f"最终时间序列数据大小: {hourly_data.shape}")
    print(f"时间跨度: {hourly_data['hour'].min()} 到 {hourly_data['hour'].max()} 小时")

    return hourly_data

def add_time_features(data):
    """添加时间特征"""
    print("正在添加时间特征...")
    
    # 创建时间索引（假设每小时一个数据点）
    data = data.copy()
    
    # 基于hour列添加时间特征
    data['hour_of_day'] = data['hour'] % 24
    data['day_of_week'] = (data['hour'] // 24) % 7
    data['week_of_month'] = ((data['hour'] // 24) // 7) % 4
    data['month'] = ((data['hour'] // 24) // 30) % 12
    
    # 周期性特征编码（sin/cos变换）
    data['hour_sin'] = np.sin(2 * np.pi * data['hour_of_day'] / 24)
    data['hour_cos'] = np.cos(2 * np.pi * data['hour_of_day'] / 24)
    data['day_sin'] = np.sin(2 * np.pi * data['day_of_week'] / 7)
    data['day_cos'] = np.cos(2 * np.pi * data['day_of_week'] / 7)
    data['month_sin'] = np.sin(2 * np.pi * data['month'] / 12)
    data['month_cos'] = np.cos(2 * np.pi * data['month'] / 12)
    
    print(f"添加时间特征后数据维度: {data.shape}")
    return data

def add_rolling_window_features(data, features=['cpu_usage', 'gpu_wrk_util', 'avg_mem']):
    """添加滑动窗口统计特征"""
    print("正在添加滑动窗口统计特征...")
    
    data = data.copy()
    windows = [3, 6, 12, 24, 48]  # 不同的窗口大小（小时）
    
    for feature in features:
        for window in windows:
            # 滑动平均
            data[f'{feature}_ma_{window}'] = data[feature].rolling(window=window, min_periods=1).mean()
            
            # 滑动标准差
            data[f'{feature}_std_{window}'] = data[feature].rolling(window=window, min_periods=1).std()
            
            # 滑动最大值
            data[f'{feature}_max_{window}'] = data[feature].rolling(window=window, min_periods=1).max()
            
            # 滑动最小值
            data[f'{feature}_min_{window}'] = data[feature].rolling(window=window, min_periods=1).min()
            
            # 滑动中位数
            data[f'{feature}_median_{window}'] = data[feature].rolling(window=window, min_periods=1).median()
        
        # 添加趋势特征
        data[f'{feature}_diff_1'] = data[feature].diff(1)  # 一阶差分
        data[f'{feature}_diff_24'] = data[feature].diff(24)  # 24小时差分
        
        # 添加变化率
        data[f'{feature}_pct_change_1'] = data[feature].pct_change(1)
        data[f'{feature}_pct_change_24'] = data[feature].pct_change(24)
        
        # 添加滞后特征
        for lag in [1, 2, 3, 6, 12, 24]:
            data[f'{feature}_lag_{lag}'] = data[feature].shift(lag)
    
    # 填充NaN值
    data = data.fillna(method='ffill').fillna(method='bfill').fillna(0)
    
    print(f"添加滑动窗口特征后数据维度: {data.shape}")
    return data

def feature_engineering(data):
    """综合特征工程"""
    print("开始特征工程...")
    
    # 添加时间特征
    data = add_time_features(data)
    
    # 添加滑动窗口特征
    data = add_rolling_window_features(data)
    
    # 添加交互特征
    data['cpu_gpu_ratio'] = data['cpu_usage'] / (data['gpu_wrk_util'] + 1e-8)
    data['cpu_mem_ratio'] = data['cpu_usage'] / (data['avg_mem'] + 1e-8)
    data['gpu_mem_ratio'] = data['gpu_wrk_util'] / (data['avg_mem'] + 1e-8)
    
    # 添加资源利用率总和
    data['total_utilization'] = data['cpu_usage'] + data['gpu_wrk_util'] + data['avg_mem']
    
    print(f"特征工程完成，最终数据维度: {data.shape}")
    return data

def analyze_data_patterns(data, output_dir):
    """分析数据模式"""
    print("正在分析数据模式...")
    
    features = ['cpu_usage', 'gpu_wrk_util', 'avg_mem']
    
    fig, axes = plt.subplots(len(features), 2, figsize=(20, 5*len(features)))
    
    for i, feature in enumerate(features):
        # 时间序列图
        axes[i, 0].plot(data['hour'], data[feature], alpha=0.7)
        axes[i, 0].set_title(f'{feature} 时间序列')
        axes[i, 0].set_xlabel('小时')
        axes[i, 0].set_ylabel(feature)
        axes[i, 0].grid(True, alpha=0.3)
        
        # 分布直方图
        axes[i, 1].hist(data[feature], bins=50, alpha=0.7, edgecolor='black')
        axes[i, 1].set_title(f'{feature} 分布')
        axes[i, 1].set_xlabel(feature)
        axes[i, 1].set_ylabel('频率')
        axes[i, 1].grid(True, alpha=0.3)
        
        # 输出统计信息
        print(f"{feature} 统计信息:")
        print(f"  均值: {data[feature].mean():.4f}")
        print(f"  标准差: {data[feature].std():.4f}")
        print(f"  最小值: {data[feature].min():.4f}")
        print(f"  最大值: {data[feature].max():.4f}")
        print(f"  变异系数: {data[feature].std()/data[feature].mean():.4f}")
        print()
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'data_analysis.png'), dpi=300, bbox_inches='tight')
    plt.show()

def split_data(data, train_ratio=0.7, val_ratio=0.15, test_ratio=0.15):
    """划分训练集、验证集和测试集"""
    print(f"划分数据集，训练集:{train_ratio*100}%, 验证集:{val_ratio*100}%, 测试集:{test_ratio*100}%")
    
    total_hours = len(data)
    train_size = int(total_hours * train_ratio)
    val_size = int(total_hours * val_ratio)
    
    train_data = data.iloc[:train_size].copy()
    val_data = data.iloc[train_size:train_size + val_size].copy()
    test_data = data.iloc[train_size + val_size:].copy()
    
    print(f"训练集大小: {len(train_data)} 小时")
    print(f"验证集大小: {len(val_data)} 小时")
    print(f"测试集大小: {len(test_data)} 小时")
    
    return train_data, val_data, test_data

def create_lstm_sequences(data, sequence_length=24):
    """为LSTM创建序列数据"""
    X, y = [], []
    for i in range(sequence_length, len(data)):
        X.append(data[i-sequence_length:i])
        y.append(data[i])
    return np.array(X), np.array(y)

def create_lstm_sequences_enhanced(data, target_feature, sequence_length=24):
    """为增强LSTM创建序列数据（包含所有特征）"""
    # 获取所有特征列（除了目标特征）
    feature_cols = [col for col in data.columns if col != 'hour' and col != target_feature]
    
    X_features, X_target, y = [], [], []
    
    for i in range(sequence_length, len(data)):
        # 提取特征序列
        X_features.append(data[feature_cols].iloc[i-sequence_length:i].values)
        
        # 提取目标变量序列
        X_target.append(data[target_feature].iloc[i-sequence_length:i].values)
        
        # 目标值
        y.append(data[target_feature].iloc[i])
    
    return np.array(X_features), np.array(X_target), np.array(y)

def build_enhanced_lstm_model(input_shape_features, input_shape_target, best_params=None):
    """构建增强的LSTM模型（使用固定超参数）"""
    # 使用固定的超参数，移除优化过程
    params = {
        'lstm_units_1': 64,
        'lstm_units_2': 32,
        'dropout_rate': 0.3,
        'learning_rate': 0.001
    }
    
    if best_params is not None:
        params.update(best_params)
    
    # 特征输入分支
    input_features = Input(shape=input_shape_features)
    lstm_features = LSTM(params['lstm_units_1'], return_sequences=True)(input_features)
    lstm_features = Dropout(params['dropout_rate'])(lstm_features)
    lstm_features = LSTM(params['lstm_units_2'])(lstm_features)
    lstm_features = Dropout(params['dropout_rate'])(lstm_features)
    
    # 目标变量输入分支
    input_target = Input(shape=input_shape_target)
    lstm_target = LSTM(params['lstm_units_2'])(input_target)
    lstm_target = Dropout(params['dropout_rate'])(lstm_target)
    
    # 合并分支
    combined = concatenate([lstm_features, lstm_target])
    dense = Dense(32, activation='relu')(combined)
    dense = Dropout(params['dropout_rate'])(dense)
    output = Dense(1)(dense)
    
    model = Model(inputs=[input_features, input_target], outputs=output)
    model.compile(
        optimizer=Adam(learning_rate=params['learning_rate']),
        loss='mse',
        metrics=['mae']
    )
    
    return model

def train_hybrid_lstm_arima_model(train_data, feature_name):
    """训练LSTM+ARIMA混合模型（使用固定超参数）"""
    print(f"训练 {feature_name} 的LSTM+ARIMA混合模型...")
    
    # 第一步：训练ARIMA模型
    print("步骤1: 训练ARIMA模型...")
    arima_model = train_arima_model(train_data, feature_name)
    
    # 获取ARIMA的拟合值和残差
    arima_fitted = arima_model.fittedvalues
    arima_residuals = train_data[feature_name].iloc[len(train_data) - len(arima_fitted):].values - arima_fitted
    
    # 将残差添加到训练数据中
    extended_data = train_data.copy()
    
    # 为了与原数据长度一致，前面补零
    residuals_padded = np.zeros(len(train_data))
    residuals_padded[-len(arima_residuals):] = arima_residuals
    extended_data['arima_residuals'] = residuals_padded
    
    # 第二步：训练增强LSTM模型（使用固定超参数）
    print("步骤2: 训练增强LSTM模型...")
    
    # 使用固定的超参数
    best_params = {
        'lstm_units_1': 64,
        'lstm_units_2': 32,
        'dropout_rate': 0.3,
        'learning_rate': 0.001,
        'batch_size': 32,
        'sequence_length': 24
    }
    
    sequence_length = best_params.get('sequence_length', 24) if best_params else 24
    
    # 数据标准化
    scaler_features = StandardScaler()
    scaler_target = MinMaxScaler()
    
    # 标准化所有特征
    feature_cols = [col for col in extended_data.columns if col not in ['hour', feature_name]]
    scaled_features = scaler_features.fit_transform(extended_data[feature_cols])
    
    # 标准化目标变量
    scaled_target = scaler_target.fit_transform(extended_data[[feature_name]])
    
    # 重新组合数据
    scaled_data = extended_data[['hour', feature_name]].copy()
    for i, col in enumerate(feature_cols):
        scaled_data[col] = scaled_features[:, i]
    scaled_data[feature_name] = scaled_target.flatten()
    
    # 创建序列
    X_features, X_target, y = create_lstm_sequences_enhanced(scaled_data, feature_name, sequence_length)
    
    if len(X_features) == 0:
        print(f"警告：训练数据不足，无法创建序列")
        return None, None, None, None, arima_model
    
    # 构建增强模型
    model = build_enhanced_lstm_model(
        (X_features.shape[1], X_features.shape[2]),
        (X_target.shape[1], 1),
        best_params
    )
    
    # 准备训练数据
    X_target_reshaped = X_target.reshape(X_target.shape[0], X_target.shape[1], 1)
    
    # 训练模型
    batch_size = best_params.get('batch_size', 32) if best_params else 32
    
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=15,
        restore_best_weights=True
    )
    
    reduce_lr = ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=7,
        min_lr=0.0001
    )
    
    history = model.fit(
        [X_features, X_target_reshaped], y,
        epochs=100,
        batch_size=batch_size,
        validation_split=0.2,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )
    
    return model, scaler_features, scaler_target, history, arima_model

def predict_hybrid_lstm_arima(model, scaler_features, scaler_target, arima_model, 
                               train_data, test_data, feature_name, sequence_length=24):
    """使用LSTM+ARIMA混合模型进行预测"""
    print(f"使用混合模型预测 {feature_name}...")
    
    # 第一步：使用ARIMA模型预测测试集
    arima_forecast = predict_arima(arima_model, len(test_data))
    
    # 第二步：计算ARIMA预测的残差作为LSTM的输入特征
    # 合并训练和测试数据进行特征工程
    combined_data = pd.concat([train_data, test_data], ignore_index=True)
    combined_data = feature_engineering(combined_data)
    
    # 添加ARIMA预测残差特征
    arima_residuals = np.zeros(len(combined_data))
    test_start_idx = len(train_data)
    
    # 对测试期间的每个点，计算ARIMA预测误差
    for i in range(len(test_data)):
        if i < len(arima_forecast):
            # 使用真实值与ARIMA预测值的差作为残差特征
            true_val = test_data[feature_name].iloc[i] if i == 0 else arima_forecast[i-1]  # 对于第一个点使用真实值
            arima_residuals[test_start_idx + i] = true_val - arima_forecast[i]
    
    combined_data['arima_residuals'] = arima_residuals
    
    # 第三步：准备LSTM输入数据
    feature_cols = [col for col in combined_data.columns if col not in ['hour', feature_name]]
    
    # 标准化特征
    scaled_features = scaler_features.transform(combined_data[feature_cols])
    scaled_target_col = scaler_target.transform(combined_data[[feature_name]])
    
    # 重新组合数据
    scaled_data = combined_data[['hour', feature_name]].copy()
    for i, col in enumerate(feature_cols):
        scaled_data[col] = scaled_features[:, i]
    scaled_data[feature_name] = scaled_target_col.flatten()
    
    # 第四步：逐步预测
    predictions = []
    
    for i in range(test_start_idx, len(combined_data)):
        # 获取输入序列
        if i >= sequence_length:
            # 特征序列
            feature_sequence = scaled_data[feature_cols].iloc[i-sequence_length:i].values
            feature_sequence = feature_sequence.reshape(1, sequence_length, len(feature_cols))
            
            # 目标变量序列
            target_sequence = scaled_data[feature_name].iloc[i-sequence_length:i].values
            target_sequence = target_sequence.reshape(1, sequence_length, 1)
            
            # LSTM预测
            lstm_pred = model.predict([feature_sequence, target_sequence], verbose=0)
            lstm_pred_scaled = lstm_pred[0, 0]
            
            # 反标准化LSTM预测
            lstm_pred_unscaled = scaler_target.inverse_transform([[lstm_pred_scaled]])[0, 0]
            
            # 第五步：结合ARIMA和LSTM预测
            test_idx = i - test_start_idx
            if test_idx < len(arima_forecast):
                # 加权组合：ARIMA预测 + LSTM预测的残差调整
                final_pred = arima_forecast[test_idx] + lstm_pred_unscaled * 0.3  # 给LSTM残差预测30%权重
            else:
                final_pred = lstm_pred_unscaled
            
            predictions.append(final_pred)
            
            # 更新序列数据以用于下一次预测
            scaled_data.iloc[i, scaled_data.columns.get_loc(feature_name)] = lstm_pred_scaled
        else:
            # 如果历史数据不足，使用ARIMA预测
            test_idx = i - test_start_idx
            if test_idx < len(arima_forecast):
                predictions.append(arima_forecast[test_idx])
            else:
                predictions.append(test_data[feature_name].iloc[test_idx])
    
    return np.array(predictions)
def train_lstm_model(train_data, feature_name, sequence_length=24):
    """训练基础LSTM模型（保留用于对比）"""
    print(f"训练 {feature_name} 的基础LSTM模型...")

    # 数据标准化
    scaler = MinMaxScaler()
    scaled_data = scaler.fit_transform(train_data[[feature_name]])

    # 创建序列
    X_train, y_train = create_lstm_sequences(scaled_data, sequence_length)
    
    if len(X_train) == 0:
        print(f"警告：训练数据不足，无法创建序列")
        return None, None, None

    # 构建基础模型
    model = Sequential([
        LSTM(64, return_sequences=True, input_shape=(X_train.shape[1], X_train.shape[2])),
        Dropout(0.3),
        LSTM(32, return_sequences=False),
        Dropout(0.3),
        Dense(25, activation='relu'),
        Dense(1)
    ])

    model.compile(optimizer=Adam(learning_rate=0.001), loss='mse', metrics=['mae'])

    # 添加早停和学习率调整
    early_stopping = EarlyStopping(
        monitor='val_loss',
        patience=10,
        restore_best_weights=True
    )
    
    reduce_lr = ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.5,
        patience=5,
        min_lr=0.0001
    )

    # 训练模型
    history = model.fit(
        X_train, y_train,
        epochs=100,
        batch_size=32,
        validation_split=0.2,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )

    return model, scaler, history

def predict_lstm(model, scaler, train_data, test_data, feature_name, sequence_length=24):
    """使用LSTM模型进行预测"""
    # 合并训练和测试数据以获得足够的历史序列
    combined_data = pd.concat([train_data, test_data], ignore_index=True)
    scaled_combined = scaler.transform(combined_data[[feature_name]])
    
    # 获取测试数据的起始位置
    test_start_idx = len(train_data)
    
    predictions = []
    
    # 对测试集中的每个点进行预测
    for i in range(test_start_idx, len(combined_data)):
        # 获取前sequence_length个时间步作为输入
        if i >= sequence_length:
            input_sequence = scaled_combined[i-sequence_length:i].reshape(1, sequence_length, 1)
        else:
            # 如果历史数据不足，用0填充
            input_sequence = np.zeros((1, sequence_length, 1))
            available_length = i
            input_sequence[0, -available_length:, 0] = scaled_combined[:i].flatten()
        
        # 预测下一个值
        pred = model.predict(input_sequence, verbose=0)
        predictions.append(pred[0, 0])
    
    # 反标准化
    predictions = scaler.inverse_transform(np.array(predictions).reshape(-1, 1))
    return predictions.flatten()

def predict_lstm_on_data(model, scaler, train_data, target_data, feature_name, sequence_length=24):
    """使用LSTM模型预测特定数据段"""
    # 合并训练数据和目标数据以获得足够的历史序列
    combined_data = pd.concat([train_data, target_data], ignore_index=True)
    scaled_combined = scaler.transform(combined_data[[feature_name]])
    
    # 获取目标数据的起始位置
    target_start_idx = len(train_data)
    
    predictions = []
    
    # 对目标数据段中的每个点进行预测
    for i in range(target_start_idx, len(combined_data)):
        # 获取前sequence_length个时间步作为输入
        if i >= sequence_length:
            input_sequence = scaled_combined[i-sequence_length:i].reshape(1, sequence_length, 1)
        else:
            # 如果历史数据不足，用0填充
            input_sequence = np.zeros((1, sequence_length, 1))
            available_length = i
            input_sequence[0, -available_length:, 0] = scaled_combined[:i].flatten()
        
        # 预测下一个值
        pred = model.predict(input_sequence, verbose=0)
        predictions.append(pred[0, 0])
    
    # 反标准化
    predictions = scaler.inverse_transform(np.array(predictions).reshape(-1, 1))
    return predictions.flatten()

def plot_train_last_72h_predictions(results, train_data, output_dir):
    """绘制训练集最后72小时的预测对比结果"""
    print("正在生成训练集最后72小时预测对比结果...")
    
    features = list(results.keys())
    
    # 获取训练集最后72小时的数据
    last_72h_data = train_data.tail(72).copy()
    
    # 创建时间轴（相对于最后72小时的开始）
    hours = range(72)
    
    # 绘制预测对比图
    fig, axes = plt.subplots(len(features), 1, figsize=(15, 6*len(features)))
    if len(features) == 1:
        axes = [axes]

    for i, feature in enumerate(features):
        ax = axes[i]
        
        # 获取真实值
        true_values = last_72h_data[feature].values
        
        # 获取预测值（如果存在）
        if f'train_last_72h_{feature}_lstm_pred' in results[feature]:
            lstm_pred = results[feature][f'train_last_72h_{feature}_lstm_pred']
            ax.plot(hours, lstm_pred, label='LSTM预测', linewidth=2, alpha=0.8, color='blue')
        
        if f'train_last_72h_{feature}_arima_pred' in results[feature]:
            arima_pred = results[feature][f'train_last_72h_{feature}_arima_pred']
            ax.plot(hours, arima_pred, label='ARIMA预测', linewidth=2, alpha=0.8, color='green')
        
        if f'train_last_72h_{feature}_hybrid_pred' in results[feature]:
            hybrid_pred = results[feature][f'train_last_72h_{feature}_hybrid_pred']
            ax.plot(hours, hybrid_pred, label='混合模型预测', linewidth=2.5, alpha=0.9, color='red')
        
        # 绘制真实值
        ax.plot(hours, true_values, label='真实值', linewidth=2.5, alpha=0.9, color='black')

        ax.set_title(f'{feature} 训练集最后72小时预测对比', fontsize=14, fontweight='bold')
        ax.set_xlabel('时间 (小时)', fontsize=12)
        ax.set_ylabel(f'{feature}', fontsize=12)
        ax.legend(fontsize=10)
        ax.grid(True, alpha=0.3)
        
        # 添加性能指标文本（如果计算了的话）
        if f'train_last_72h_{feature}_lstm_metrics' in results[feature]:
            lstm_metrics = results[feature][f'train_last_72h_{feature}_lstm_metrics']
            arima_metrics = results[feature][f'train_last_72h_{feature}_arima_metrics']
            hybrid_metrics = results[feature][f'train_last_72h_{feature}_hybrid_metrics']
            
            metrics_text = f'LSTM R²: {lstm_metrics["R²"]:.3f}\n'
            metrics_text += f'ARIMA R²: {arima_metrics["R²"]:.3f}\n'
            metrics_text += f'混合模型 R²: {hybrid_metrics["R²"]:.3f}'
            
            ax.text(0.02, 0.98, metrics_text, transform=ax.transAxes, fontsize=10,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'train_last_72h_predictions.png'), dpi=300, bbox_inches='tight')
    plt.close()
    print(f"训练集最后72小时预测对比图已保存")

def plot_training_loss(training_histories, output_dir):
    """绘制并保存训练损失图"""
    print("正在生成训练损失图...")
    
    # 创建子图
    features = list(training_histories.keys())
    fig, axes = plt.subplots(len(features), 2, figsize=(15, 5*len(features)))
    
    if len(features) == 1:
        axes = axes.reshape(1, -1)
    
    for i, feature in enumerate(features):
        if 'lstm_history' in training_histories[feature] and training_histories[feature]['lstm_history'] is not None:
            history = training_histories[feature]['lstm_history']
            
            # 训练和验证损失
            ax1 = axes[i, 0]
            ax1.plot(history.history['loss'], label='训练损失', color='blue', linewidth=2)
            ax1.plot(history.history['val_loss'], label='验证损失', color='red', linewidth=2)
            ax1.set_title(f'{feature} - LSTM训练损失', fontsize=12, fontweight='bold')
            ax1.set_xlabel('Epoch')
            ax1.set_ylabel('损失值')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            
            # 训练和验证MAE
            ax2 = axes[i, 1]
            ax2.plot(history.history['mae'], label='训练MAE', color='blue', linewidth=2)
            ax2.plot(history.history['val_mae'], label='验证MAE', color='red', linewidth=2)
            ax2.set_title(f'{feature} - LSTM训练MAE', fontsize=12, fontweight='bold')
            ax2.set_xlabel('Epoch')
            ax2.set_ylabel('MAE')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
        
        if 'hybrid_history' in training_histories[feature] and training_histories[feature]['hybrid_history'] is not None:
            # 如果有混合模型的历史，也可以在这里绘制
            pass
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'loss_his.png'), dpi=300, bbox_inches='tight')
    plt.close()
    print(f"训练损失图已保存为 loss_his.png")

def calculate_training_statistics(results, training_histories, output_dir):
    """计算并保存训练评估指标统计"""
    print("正在计算训练评估指标统计...")
    
    stats_data = []
    
    for feature in results.keys():
        # 验证集指标统计
        if f'val_lstm_metrics' in results[feature]:
            val_lstm_metrics = results[feature]['val_lstm_metrics']
            val_arima_metrics = results[feature]['val_arima_metrics']
            val_hybrid_metrics = results[feature]['val_hybrid_metrics']
            
            stats_data.append({
                'Feature': feature,
                'Dataset': '验证集',
                'Model': 'LSTM',
                'MSE': val_lstm_metrics['MSE'],
                'RMSE': val_lstm_metrics['RMSE'],
                'MAE': val_lstm_metrics['MAE'],
                'R²': val_lstm_metrics['R²']
            })
            
            stats_data.append({
                'Feature': feature,
                'Dataset': '验证集',
                'Model': 'ARIMA',
                'MSE': val_arima_metrics['MSE'],
                'RMSE': val_arima_metrics['RMSE'],
                'MAE': val_arima_metrics['MAE'],
                'R²': val_arima_metrics['R²']
            })
            
            stats_data.append({
                'Feature': feature,
                'Dataset': '验证集',
                'Model': '混合模型',
                'MSE': val_hybrid_metrics['MSE'],
                'RMSE': val_hybrid_metrics['RMSE'],
                'MAE': val_hybrid_metrics['MAE'],
                'R²': val_hybrid_metrics['R²']
            })
        
        # 测试集指标统计
        if f'test_lstm_metrics' in results[feature]:
            test_lstm_metrics = results[feature]['test_lstm_metrics']
            test_arima_metrics = results[feature]['test_arima_metrics']
            test_hybrid_metrics = results[feature]['test_hybrid_metrics']
            
            stats_data.append({
                'Feature': feature,
                'Dataset': '测试集',
                'Model': 'LSTM',
                'MSE': test_lstm_metrics['MSE'],
                'RMSE': test_lstm_metrics['RMSE'],
                'MAE': test_lstm_metrics['MAE'],
                'R²': test_lstm_metrics['R²']
            })
            
            stats_data.append({
                'Feature': feature,
                'Dataset': '测试集',
                'Model': 'ARIMA',
                'MSE': test_arima_metrics['MSE'],
                'RMSE': test_arima_metrics['RMSE'],
                'MAE': test_arima_metrics['MAE'],
                'R²': test_arima_metrics['R²']
            })
            
            stats_data.append({
                'Feature': feature,
                'Dataset': '测试集',
                'Model': '混合模型',
                'MSE': test_hybrid_metrics['MSE'],
                'RMSE': test_hybrid_metrics['RMSE'],
                'MAE': test_hybrid_metrics['MAE'],
                'R²': test_hybrid_metrics['R²']
            })
        
        # 训练集最后72小时指标统计（如果存在）
        if f'train_last_72h_{feature}_lstm_metrics' in results[feature]:
            train_lstm_metrics = results[feature][f'train_last_72h_{feature}_lstm_metrics']
            train_arima_metrics = results[feature][f'train_last_72h_{feature}_arima_metrics']
            train_hybrid_metrics = results[feature][f'train_last_72h_{feature}_hybrid_metrics']
            
            stats_data.append({
                'Feature': feature,
                'Dataset': '训练集最后72h',
                'Model': 'LSTM',
                'MSE': train_lstm_metrics['MSE'],
                'RMSE': train_lstm_metrics['RMSE'],
                'MAE': train_lstm_metrics['MAE'],
                'R²': train_lstm_metrics['R²']
            })
            
            stats_data.append({
                'Feature': feature,
                'Dataset': '训练集最后72h',
                'Model': 'ARIMA',
                'MSE': train_arima_metrics['MSE'],
                'RMSE': train_arima_metrics['RMSE'],
                'MAE': train_arima_metrics['MAE'],
                'R²': train_arima_metrics['R²']
            })
            
            stats_data.append({
                'Feature': feature,
                'Dataset': '训练集最后72h',
                'Model': '混合模型',
                'MSE': train_hybrid_metrics['MSE'],
                'RMSE': train_hybrid_metrics['RMSE'],
                'MAE': train_hybrid_metrics['MAE'],
                'R²': train_hybrid_metrics['R²']
            })
    
    # 保存统计结果
    stats_df = pd.DataFrame(stats_data)
    stats_df.to_csv(os.path.join(output_dir, 'training_evaluation_statistics.csv'), 
                   index=False, encoding='utf-8-sig')
    
    # 计算汇总统计
    summary_stats = []
    for dataset in ['验证集', '测试集', '训练集最后72h']:
        dataset_data = stats_df[stats_df['Dataset'] == dataset]
        if not dataset_data.empty:
            for model in ['LSTM', 'ARIMA', '混合模型']:
                model_data = dataset_data[dataset_data['Model'] == model]
                if not model_data.empty:
                    summary_stats.append({
                        'Dataset': dataset,
                        'Model': model,
                        'Avg_MSE': model_data['MSE'].mean(),
                        'Avg_RMSE': model_data['RMSE'].mean(),
                        'Avg_MAE': model_data['MAE'].mean(),
                        'Avg_R²': model_data['R²'].mean(),
                        'Std_MSE': model_data['MSE'].std(),
                        'Std_RMSE': model_data['RMSE'].std(),
                        'Std_MAE': model_data['MAE'].std(),
                        'Std_R²': model_data['R²'].std(),
                    })
    
    summary_df = pd.DataFrame(summary_stats)
    summary_df.to_csv(os.path.join(output_dir, 'training_evaluation_summary.csv'),
                     index=False, encoding='utf-8-sig')
    
    print(f"训练评估指标统计已保存:")
    print(f"- training_evaluation_statistics.csv: 详细指标统计")
    print(f"- training_evaluation_summary.csv: 汇总统计")

def train_and_evaluate(output_dir):
    """训练和评估模型"""
    print("=== 开始模型训练和评估 ===")
    
    # 加载和预处理数据
    data = load_and_preprocess_data()
    
    # 特征工程
    data_with_features = feature_engineering(data)
    
    # 划分训练集、验证集和测试集
    train_data, val_data, test_data = split_data(data_with_features)

    # 特征列表
    features = ['cpu_usage', 'gpu_wrk_util', 'avg_mem']

    # 存储结果
    results = {}
    training_histories = {}

    for feature in features:
        print(f"\n处理特征: {feature}")
        results[feature] = {}
        training_histories[feature] = {}

        # 基础LSTM模型
        print("训练基础LSTM模型...")
        lstm_model, lstm_scaler, lstm_history = train_lstm_model(train_data, feature)
        training_histories[feature]['lstm_history'] = lstm_history
        
        val_lstm_predictions = predict_lstm(lstm_model, lstm_scaler, train_data, val_data, feature)
        test_lstm_predictions = predict_lstm(lstm_model, lstm_scaler, train_data, test_data, feature)
        
        # 训练集最后72小时预测
        print("预测训练集最后72小时...")
        train_last_72h_data = train_data.tail(72).copy()
        train_last_72h_lstm_pred = predict_lstm_on_data(lstm_model, lstm_scaler, 
                                                       train_data.iloc[:-72], train_last_72h_data, feature)
        results[feature][f'train_last_72h_{feature}_lstm_pred'] = train_last_72h_lstm_pred

        # ARIMA模型
        print("训练ARIMA模型...")
        arima_model = train_arima_model(train_data, feature)
        val_arima_predictions = predict_arima(arima_model, len(val_data))
        test_arima_predictions = predict_arima(arima_model, len(test_data))

        # LSTM+ARIMA混合模型
        print("训练LSTM+ARIMA混合模型...")
        try:
            hybrid_model, hybrid_scaler_features, hybrid_scaler_target, hybrid_history, _ = train_hybrid_lstm_arima_model(
                train_data, feature
            )
            
            if hybrid_model is not None:
                val_hybrid_predictions = predict_hybrid_lstm_arima(
                    hybrid_model, hybrid_scaler_features, hybrid_scaler_target,
                    arima_model, train_data, val_data, feature
                )
                test_hybrid_predictions = predict_hybrid_lstm_arima(
                    hybrid_model, hybrid_scaler_features, hybrid_scaler_target,
                    arima_model, train_data, test_data, feature
                )
            else:
                print("混合模型训练失败，使用LSTM预测作为替代")
                val_hybrid_predictions = val_lstm_predictions
                test_hybrid_predictions = test_lstm_predictions
        except Exception as e:
            print(f"混合模型训练出错: {e}")
            print("使用LSTM预测作为混合模型替代")
            val_hybrid_predictions = val_lstm_predictions
            test_hybrid_predictions = test_lstm_predictions

        # 验证集真实值和测试集真实值
        val_y_true = val_data[feature].values
        test_y_true = test_data[feature].values

        # 确保预测结果长度一致
        # 验证集
        val_min_length = min(len(val_y_true), len(val_lstm_predictions), len(val_arima_predictions), len(val_hybrid_predictions))
        val_y_true = val_y_true[:val_min_length]
        val_lstm_predictions = val_lstm_predictions[:val_min_length]
        val_arima_predictions = val_arima_predictions[:val_min_length]
        val_hybrid_predictions = val_hybrid_predictions[:val_min_length]
        
        # 测试集
        test_min_length = min(len(test_y_true), len(test_lstm_predictions), len(test_arima_predictions), len(test_hybrid_predictions))
        test_y_true = test_y_true[:test_min_length]
        test_lstm_predictions = test_lstm_predictions[:test_min_length]
        test_arima_predictions = test_arima_predictions[:test_min_length]
        test_hybrid_predictions = test_hybrid_predictions[:test_min_length]

        # 计算验证集指标
        val_lstm_metrics = calculate_metrics(val_y_true, val_lstm_predictions)
        val_arima_metrics = calculate_metrics(val_y_true, val_arima_predictions)
        val_hybrid_metrics = calculate_metrics(val_y_true, val_hybrid_predictions)
        
        # 计算测试集指标
        test_lstm_metrics = calculate_metrics(test_y_true, test_lstm_predictions)
        test_arima_metrics = calculate_metrics(test_y_true, test_arima_predictions)
        test_hybrid_metrics = calculate_metrics(test_y_true, test_hybrid_predictions)

        results[feature] = {
            'val_y_true': val_y_true,
            'test_y_true': test_y_true,
            'val_lstm_pred': val_lstm_predictions,
            'test_lstm_pred': test_lstm_predictions,
            'val_arima_pred': val_arima_predictions,
            'test_arima_pred': test_arima_predictions,
            'val_hybrid_pred': val_hybrid_predictions,
            'test_hybrid_pred': test_hybrid_predictions,
            'val_lstm_metrics': val_lstm_metrics,
            'test_lstm_metrics': test_lstm_metrics,
            'val_arima_metrics': val_arima_metrics,
            'test_arima_metrics': test_arima_metrics,
            'val_hybrid_metrics': val_hybrid_metrics,
            'test_hybrid_metrics': test_hybrid_metrics
        }

        print(f"验证集指标:")
        print(f"LSTM - MSE: {val_lstm_metrics['MSE']:.4f}, RMSE: {val_lstm_metrics['RMSE']:.4f}, MAE: {val_lstm_metrics['MAE']:.4f}, R²: {val_lstm_metrics['R²']:.4f}")
        print(f"ARIMA - MSE: {val_arima_metrics['MSE']:.4f}, RMSE: {val_arima_metrics['RMSE']:.4f}, MAE: {val_arima_metrics['MAE']:.4f}, R²: {val_arima_metrics['R²']:.4f}")
        print(f"混合模型 - MSE: {val_hybrid_metrics['MSE']:.4f}, RMSE: {val_hybrid_metrics['RMSE']:.4f}, MAE: {val_hybrid_metrics['MAE']:.4f}, R²: {val_hybrid_metrics['R²']:.4f}")
        
        print(f"测试集指标:")
        print(f"LSTM - MSE: {test_lstm_metrics['MSE']:.4f}, RMSE: {test_lstm_metrics['RMSE']:.4f}, MAE: {test_lstm_metrics['MAE']:.4f}, R²: {test_lstm_metrics['R²']:.4f}")
        print(f"ARIMA - MSE: {test_arima_metrics['MSE']:.4f}, RMSE: {test_arima_metrics['RMSE']:.4f}, MAE: {test_arima_metrics['MAE']:.4f}, R²: {test_arima_metrics['R²']:.4f}")
        print(f"混合模型 - MSE: {test_hybrid_metrics['MSE']:.4f}, RMSE: {test_hybrid_metrics['RMSE']:.4f}, MAE: {test_hybrid_metrics['MAE']:.4f}, R²: {test_hybrid_metrics['R²']:.4f}")

        # 保存训练历史
        training_histories[feature] = {
            'lstm_history': lstm_history,
            'hybrid_history': hybrid_history if 'hybrid_history' in locals() else None
        }

    print("\n=== 生成可视化结果 ===")

    # 绘制预测结果对比（只显示验证集和测试集）
    plot_val_test_predictions(results, val_data, test_data, output_dir)

    # 绘制指标对比（验证集和测试集）
    plot_val_test_metrics_comparison(results, output_dir)

    # 保存结果到CSV（验证集和测试集）
    save_val_test_results_to_csv(results, output_dir)

    # 绘制训练损失图
    plot_training_loss(training_histories, output_dir)

    # 计算训练评估指标统计
    calculate_training_statistics(results, training_histories, output_dir)

    print(f"\n=== 程序完成 ===")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"所有结果已保存到: {output_dir}")
    print(f"生成的文件:")
    print(f"- main_fixed.py: 源代码")
    print(f"- data_analysis.png: 数据分析图")
    print(f"- val_test_prediction_comparison.png: 验证集和测试集预测结果对比图")
    print(f"- val_test_metrics_comparison.png: 验证集和测试集指标对比图")
    print(f"- val_test_metrics_summary.csv: 验证集和测试集指标摘要")
    print(f"- [feature]_val_test_predictions.csv: 各特征的验证集和测试集预测详细数据")
    print(f"- loss_his.png: 训练损失图")
    print(f"- training_evaluation_statistics.csv: 训练评估指标详细统计")
    print(f"- training_evaluation_summary.csv: 训练评估指标汇总统计")
    print(f"\n主要改进:")
    print(f"- 移除了超参数优化，使用固定参数加快训练")
    print(f"- 使用70%/15%/15%的数据分割比例")
    print(f"- 添加了Z-score异常值检测和移除")
    print(f"- 分别评估验证集和测试集性能")
    print(f"- 可视化只显示验证集和测试集预测结果")

def main():
    """主函数"""
    print("=== PAI集群时间序列预测程序（增强版本） ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 创建输出目录
    output_dir = create_output_directory()
    print(f"输出目录: {output_dir}")

    # 保存代码
    save_code_to_output(output_dir)

    # 加载和预处理数据
    data = load_and_preprocess_data()
    
    # 特征工程
    data_with_features = feature_engineering(data)
    
    # 分析数据模式
    analyze_data_patterns(data_with_features, output_dir)

    # 划分训练集、验证集和测试集
    train_data, val_data, test_data = split_data(data_with_features)

    # 特征列表
    features = ['cpu_usage', 'gpu_wrk_util', 'avg_mem']

    # 存储结果
    results = {}
    training_histories = {}

    print("\n=== 开始模型训练和预测 ===")

    for feature in features:
        print(f"\n处理特征: {feature}")
        results[feature] = {}

        # 基础LSTM模型
        print("训练基础LSTM模型...")
        lstm_model, lstm_scaler, lstm_history = train_lstm_model(train_data, feature)
        val_lstm_predictions = predict_lstm(lstm_model, lstm_scaler, train_data, val_data, feature)
        test_lstm_predictions = predict_lstm(lstm_model, lstm_scaler, train_data, test_data, feature)

        # ARIMA模型
        print("训练ARIMA模型...")
        arima_model = train_arima_model(train_data, feature)
        val_arima_predictions = predict_arima(arima_model, len(val_data))
        test_arima_predictions = predict_arima(arima_model, len(test_data))

        # LSTM+ARIMA混合模型
        print("训练LSTM+ARIMA混合模型...")
        try:
            hybrid_model, hybrid_scaler_features, hybrid_scaler_target, hybrid_history, _ = train_hybrid_lstm_arima_model(
                train_data, feature
            )
            
            if hybrid_model is not None:
                val_hybrid_predictions = predict_hybrid_lstm_arima(
                    hybrid_model, hybrid_scaler_features, hybrid_scaler_target,
                    arima_model, train_data, val_data, feature
                )
                test_hybrid_predictions = predict_hybrid_lstm_arima(
                    hybrid_model, hybrid_scaler_features, hybrid_scaler_target,
                    arima_model, train_data, test_data, feature
                )
            else:
                print("混合模型训练失败，使用LSTM预测作为替代")
                val_hybrid_predictions = val_lstm_predictions
                test_hybrid_predictions = test_lstm_predictions
        except Exception as e:
            print(f"混合模型训练出错: {e}")
            print("使用LSTM预测作为混合模型替代")
            val_hybrid_predictions = val_lstm_predictions
            test_hybrid_predictions = test_lstm_predictions

        # 验证集真实值和测试集真实值
        val_y_true = val_data[feature].values
        test_y_true = test_data[feature].values

        # 确保预测结果长度一致
        # 验证集
        val_min_length = min(len(val_y_true), len(val_lstm_predictions), len(val_arima_predictions), len(val_hybrid_predictions))
        val_y_true = val_y_true[:val_min_length]
        val_lstm_predictions = val_lstm_predictions[:val_min_length]
        val_arima_predictions = val_arima_predictions[:val_min_length]
        val_hybrid_predictions = val_hybrid_predictions[:val_min_length]
        
        # 测试集
        test_min_length = min(len(test_y_true), len(test_lstm_predictions), len(test_arima_predictions), len(test_hybrid_predictions))
        test_y_true = test_y_true[:test_min_length]
        test_lstm_predictions = test_lstm_predictions[:test_min_length]
        test_arima_predictions = test_arima_predictions[:test_min_length]
        test_hybrid_predictions = test_hybrid_predictions[:test_min_length]

        # 计算验证集指标
        val_lstm_metrics = calculate_metrics(val_y_true, val_lstm_predictions)
        val_arima_metrics = calculate_metrics(val_y_true, val_arima_predictions)
        val_hybrid_metrics = calculate_metrics(val_y_true, val_hybrid_predictions)
        
        # 计算测试集指标
        test_lstm_metrics = calculate_metrics(test_y_true, test_lstm_predictions)
        test_arima_metrics = calculate_metrics(test_y_true, test_arima_predictions)
        test_hybrid_metrics = calculate_metrics(test_y_true, test_hybrid_predictions)

        results[feature] = {
            'val_y_true': val_y_true,
            'test_y_true': test_y_true,
            'val_lstm_pred': val_lstm_predictions,
            'test_lstm_pred': test_lstm_predictions,
            'val_arima_pred': val_arima_predictions,
            'test_arima_pred': test_arima_predictions,
            'val_hybrid_pred': val_hybrid_predictions,
            'test_hybrid_pred': test_hybrid_predictions,
            'val_lstm_metrics': val_lstm_metrics,
            'test_lstm_metrics': test_lstm_metrics,
            'val_arima_metrics': val_arima_metrics,
            'test_arima_metrics': test_arima_metrics,
            'val_hybrid_metrics': val_hybrid_metrics,
            'test_hybrid_metrics': test_hybrid_metrics
        }

        print(f"验证集指标:")
        print(f"LSTM - MSE: {val_lstm_metrics['MSE']:.4f}, RMSE: {val_lstm_metrics['RMSE']:.4f}, MAE: {val_lstm_metrics['MAE']:.4f}, R²: {val_lstm_metrics['R²']:.4f}")
        print(f"ARIMA - MSE: {val_arima_metrics['MSE']:.4f}, RMSE: {val_arima_metrics['RMSE']:.4f}, MAE: {val_arima_metrics['MAE']:.4f}, R²: {val_arima_metrics['R²']:.4f}")
        print(f"混合模型 - MSE: {val_hybrid_metrics['MSE']:.4f}, RMSE: {val_hybrid_metrics['RMSE']:.4f}, MAE: {val_hybrid_metrics['MAE']:.4f}, R²: {val_hybrid_metrics['R²']:.4f}")
        
        print(f"测试集指标:")
        print(f"LSTM - MSE: {test_lstm_metrics['MSE']:.4f}, RMSE: {test_lstm_metrics['RMSE']:.4f}, MAE: {test_lstm_metrics['MAE']:.4f}, R²: {test_lstm_metrics['R²']:.4f}")
        print(f"ARIMA - MSE: {test_arima_metrics['MSE']:.4f}, RMSE: {test_arima_metrics['RMSE']:.4f}, MAE: {test_arima_metrics['MAE']:.4f}, R²: {test_arima_metrics['R²']:.4f}")
        print(f"混合模型 - MSE: {test_hybrid_metrics['MSE']:.4f}, RMSE: {test_hybrid_metrics['RMSE']:.4f}, MAE: {test_hybrid_metrics['MAE']:.4f}, R²: {test_hybrid_metrics['R²']:.4f}")

        # 保存训练历史
        training_histories[feature] = {
            'lstm_history': lstm_history,
            'hybrid_history': hybrid_history if 'hybrid_history' in locals() else None
        }

    print("\n=== 生成可视化结果 ===")

    # 绘制预测结果对比（只显示验证集和测试集）
    plot_val_test_predictions(results, val_data, test_data, output_dir)

    # 绘制指标对比（验证集和测试集）
    plot_val_test_metrics_comparison(results, output_dir)

    # 保存结果到CSV（验证集和测试集）
    save_val_test_results_to_csv(results, output_dir)

    # 绘制训练损失图
    plot_training_loss(training_histories, output_dir)

    # 计算训练评估指标统计
    calculate_training_statistics(results, training_histories, output_dir)

    print(f"\n=== 程序完成 ===")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"所有结果已保存到: {output_dir}")
    print(f"生成的文件:")
    print(f"- main_fixed.py: 源代码")
    print(f"- data_analysis.png: 数据分析图")
    print(f"- val_test_prediction_comparison.png: 验证集和测试集预测结果对比图")
    print(f"- val_test_metrics_comparison.png: 验证集和测试集指标对比图")
    print(f"- val_test_metrics_summary.csv: 验证集和测试集指标摘要")
    print(f"- [feature]_val_test_predictions.csv: 各特征的验证集和测试集预测详细数据")
    print(f"- loss_his.png: 训练损失图")
    print(f"- training_evaluation_statistics.csv: 训练评估指标详细统计")
    print(f"- training_evaluation_summary.csv: 训练评估指标汇总统计")
    print(f"\n主要改进:")
    print(f"- 移除了超参数优化，使用固定参数加快训练")
    print(f"- 使用70%/15%/15%的数据分割比例")
    print(f"- 添加了Z-score异常值检测和移除")
    print(f"- 分别评估验证集和测试集性能")
    print(f"- 可视化只显示验证集和测试集预测结果")

if __name__ == "__main__":
    main()

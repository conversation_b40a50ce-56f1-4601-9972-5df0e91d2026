import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib参数
plt.rcParams['font.size'] = 12
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False

def get_df(filepath):
    """
    读取CSV文件的函数，参考官方代码
    """
    try:
        # 检查是否存在对应的header文件
        header_filepath = filepath.replace('.csv', '.header')
        
        # 尝试读取header文件获取列名
        try:
            with open(header_filepath, 'r', encoding='utf-8') as f:
                headers = f.read().strip().split(',')
            print(f"从 {header_filepath} 读取到列名: {headers}")
            
            # 使用自定义列名读取CSV（不跳过第一行，因为CSV文件没有header）
            df = pd.read_csv(filepath, names=headers, header=None)
        except FileNotFoundError:
            print(f"未找到header文件 {header_filepath}，使用默认列名")
            df = pd.read_csv(filepath)
        except Exception as header_error:
            print(f"读取header文件失败: {header_error}，使用默认列名")
            df = pd.read_csv(filepath)
        
        print(f"成功加载 {filepath}，数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"加载文件 {filepath} 失败: {e}")
        return None

def get_dfa(dft, dfj, dfi, dfg):
    """
    合并任务相关数据表，参考官方代码
    """
    print("开始合并任务数据表...")
    
    # 检查数据表的列名
    print(f"任务表 (dft) 列名: {list(dft.columns)}")
    print(f"作业表 (dfj) 列名: {list(dfj.columns)}")
    print(f"实例表 (dfi) 列名: {list(dfi.columns)}")
    
    # 根据header文件，我们知道所有表都有job_name列
    job_key = 'job_name'
    task_key = 'task_name'
    
    # 检查键是否存在
    if job_key not in dft.columns or job_key not in dfj.columns:
        print("错误: job_name列不存在，无法合并数据表")
        return dft.copy()
    
    print(f"使用 {job_key} 作为作业合并键")
    # 合并任务表和作业表
    dfa = dft.merge(dfj, on=job_key, how='left')
    
    # 合并实例表获取资源信息
    if job_key in dfi.columns and task_key in dfi.columns:
        print(f"使用 [{job_key}, {task_key}] 作为实例表合并键")
        
        # 从实例表中聚合plan_cpu, plan_gpu, plan_mem（这些在task表中已有）
        # 实例表主要提供worker_name和machine信息
        instance_info = dfi[[job_key, task_key, 'worker_name', 'machine']].drop_duplicates()
        dfa = dfa.merge(instance_info, on=[job_key, task_key], how='left')
    else:
        print("警告: 无法合并实例表，缺少必要的键列")
    
    # 合并标签表
    if dfg is not None and not dfg.empty and job_key in dfg.columns:
        print(f"合并标签表使用键: {job_key}")
        dfa = dfa.merge(dfg, on=job_key, how='left')
    
    # 时间处理
    time_cols = ['start_time', 'end_time']
    available_time_cols = [col for col in time_cols if col in dfa.columns]
    
    print(f"可用的时间列: {available_time_cols}")
    
    if 'start_time' in dfa.columns:
        dfa['start_date'] = pd.to_datetime(dfa['start_time'], unit='s')
    if 'end_time' in dfa.columns:
        dfa['end_date'] = pd.to_datetime(dfa['end_time'], unit='s')
        if 'start_time' in dfa.columns:
            dfa['runtime'] = dfa['end_time'] - dfa['start_time']
            dfa['runtime_i'] = dfa['runtime']
    
    print(f"任务数据表合并完成，最终形状: {dfa.shape}")
    return dfa

def get_dfw(dfi, dft, dfg):
    """
    合并工作器相关数据表，参考官方代码
    """
    print("开始合并工作器数据表...")
    
    # 检查数据表的列名
    print(f"实例表 (dfi) 列名: {list(dfi.columns)}")
    print(f"任务表 (dft) 列名: {list(dft.columns)}")
    
    # 从实例表开始
    dfw = dfi.copy()
    
    # 根据header文件，我们知道合并键
    job_key = 'job_name'
    task_key = 'task_name'
    
    # 合并任务表获取更多信息
    if job_key in dfi.columns and task_key in dfi.columns and job_key in dft.columns and task_key in dft.columns:
        print(f"使用 [{job_key}, {task_key}] 合并任务表")
        
        # 选择任务表中的有用列
        task_cols = [job_key, task_key]
        for col in ['inst_num', 'plan_cpu', 'plan_mem', 'plan_gpu', 'gpu_type']:
            if col in dft.columns:
                task_cols.append(col)
        
        task_subset = dft[task_cols].drop_duplicates()
        dfw = dfw.merge(task_subset, on=[job_key, task_key], how='left')
    else:
        print("警告: 无法合并任务表，缺少必要的键列")
    
    # 合并标签表
    if dfg is not None and not dfg.empty and job_key in dfg.columns:
        dfw = dfw.merge(dfg, on=job_key, how='left')
    
    # 时间处理
    if 'start_time' in dfw.columns:
        dfw['start_date'] = pd.to_datetime(dfw['start_time'], unit='s')
    if 'end_time' in dfw.columns:
        dfw['end_date'] = pd.to_datetime(dfw['end_time'], unit='s')
    
    print(f"工作器数据表合并完成，最终形状: {dfw.shape}")
    return dfw

def load_and_process_data():
    """
    加载并处理所有数据表
    """
    print("开始加载数据...")
    
    DATA_DIR = './data/'
    
    # 加载所有数据表
    dfj = get_df(DATA_DIR + 'pai_job_table.csv')
    dft = get_df(DATA_DIR + 'pai_task_table.csv') 
    dfi = get_df(DATA_DIR + 'pai_instance_table.csv')
    dfs = get_df(DATA_DIR + 'pai_sensor_table.csv')
    dfg = get_df(DATA_DIR + 'pai_group_tag_table.csv')
    dfp = get_df(DATA_DIR + 'pai_machine_spec.csv')
    dfm = get_df(DATA_DIR + 'pai_machine_metric.csv')
    
    # 检查关键数据表是否加载成功
    critical_tables = [dft, dfi, dfs]
    critical_names = ['task_table', 'instance_table', 'sensor_table']
    
    for table, name in zip(critical_tables, critical_names):
        if table is None:
            print(f"关键数据表 {name} 加载失败")
            return None, None, None
    
    print("数据表加载完成，开始合并...")
    
    # 合并数据表（允许某些表为空）
    try:
        dfa = get_dfa(dft, dfj if dfj is not None else pd.DataFrame(), 
                      dfi, dfg if dfg is not None else pd.DataFrame())
        dfw = get_dfw(dfi, dft, dfg if dfg is not None else pd.DataFrame())
    except Exception as e:
        print(f"数据表合并失败: {e}")
        return None, None, None
    
    # 创建带传感器数据的工作器表
    print("开始合并传感器数据...")
    dfws = dfw.copy()
      # 安全地合并机器规格表
    if dfp is not None and not dfp.empty:
        # 根据header文件，machine是合并键
        machine_key = 'machine'
        
        if machine_key in dfws.columns and machine_key in dfp.columns:
            print(f"使用 {machine_key} 合并机器规格表")
            # 移除可能重复的列
            dfp_cols = [col for col in dfp.columns if col not in dfws.columns or col == machine_key]
            dfws = dfws.merge(dfp[dfp_cols], on=machine_key, how='left')
        else:
            print("警告: 无法合并机器规格表，缺少合并键")
    
    # 安全地合并传感器数据
    if dfs is not None and not dfs.empty:
        # 根据header文件，worker_name是合并键
        worker_key = 'worker_name'
        
        if worker_key in dfws.columns and worker_key in dfs.columns:
            print(f"使用 {worker_key} 合并传感器数据")
            # 移除可能重复的列
            dfs_cols = [col for col in dfs.columns if col not in dfws.columns or col == worker_key]
            dfws = dfws.merge(dfs[dfs_cols], on=worker_key, how='left')
        else:
            print("警告: 无法合并传感器数据，缺少合并键")
    
    print(f"带传感器数据的工作器表创建完成，形状: {dfws.shape}")
    print(f"可用列: {list(dfws.columns)}")
    
    return dfa, dfw, dfws

def extract_timeseries_data(dfws):
    """
    从dfws中提取时间序列数据
    """
    print("开始提取时间序列数据...")
    
    # 选择时间序列相关的列
    timeseries_columns = [
        'start_date', 'end_date', 'start_time', 'end_time',
        'cpu_usage', 'gpu_wrk_util', 'avg_mem', 'avg_gpu_wrk_mem',
        'plan_cpu', 'plan_gpu', 'plan_mem',
        'worker_name', 'machine', 'job_name', 'task_name', 'user'
    ]
    
    # 检查哪些列在数据中存在
    available_columns = [col for col in timeseries_columns if col in dfws.columns]
    print(f"可用的时间序列列: {available_columns}")
    
    # 提取数据
    ts_data = dfws[available_columns].copy()
    
    # 数据清洗
    print(f"原始数据形状: {ts_data.shape}")
    
    # 确保关键指标至少有一个不为空
    key_metrics = ['cpu_usage', 'gpu_wrk_util', 'avg_mem']
    available_metrics = [col for col in key_metrics if col in ts_data.columns]
    
    if available_metrics:
        ts_data = ts_data.dropna(subset=available_metrics, how='all')
        print(f"清洗后数据形状: {ts_data.shape}")
    
    # 时间处理
    if 'start_date' in ts_data.columns:
        ts_data['start_date'] = pd.to_datetime(ts_data['start_date'])
        ts_data = ts_data.sort_values('start_date')
        
        # 添加小时信息用于聚合
        ts_data['hour'] = ts_data['start_date'].dt.floor('H')
        ts_data['day'] = ts_data['start_date'].dt.date
        ts_data['hour_of_day'] = ts_data['start_date'].dt.hour
    
    # 数据统计
    print("\n数据统计信息:")
    for metric in available_metrics:
        if metric in ts_data.columns:
            print(f"{metric}: 均值={ts_data[metric].mean():.2f}, "
                  f"中位数={ts_data[metric].median():.2f}, "
                  f"最大值={ts_data[metric].max():.2f}")
    
    # 保存到clean.csv
    output_file = 'clean.csv'
    ts_data.to_csv(output_file, index=False)
    print(f"\n时间序列数据已保存到: {output_file}")
    
    return ts_data

def visualize_cluster_load(ts_data):
    """
    可视化集群负载随时间的变化
    """
    print("开始绘制可视化图像...")
    
    # 检查可用的指标
    metrics = ['cpu_usage', 'gpu_wrk_util', 'avg_mem']
    available_metrics = [m for m in metrics if m in ts_data.columns and ts_data[m].notna().any()]
    
    if not available_metrics:
        print("没有可用的指标数据进行可视化")
        return
    
    # 按小时聚合数据
    if 'hour' in ts_data.columns:
        hourly_data = ts_data.groupby('hour').agg({
            metric: 'mean' for metric in available_metrics
        }).reset_index()
        
        # 限制显示最近7天的数据
        if len(hourly_data) > 168:  # 7天*24小时
            hourly_data = hourly_data.tail(168)
    else:
        hourly_data = ts_data.copy()
    
    # 创建子图
    n_metrics = len(available_metrics)
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('集群负载时间序列分析', fontsize=16)
    
    # 扁平化axes以便统一处理
    axes_flat = axes.flatten()
    
    # 绘制各个指标的时间序列
    colors = ['blue', 'red', 'green', 'orange']
    titles = ['CPU使用率随时间变化', 'GPU使用率随时间变化', '内存使用量随时间变化']
    
    for i, metric in enumerate(available_metrics):
        if i < 3:  # 前3个子图用于单独指标
            ax = axes_flat[i]
            data_to_plot = hourly_data[metric].dropna()
            
            if len(data_to_plot) > 0:
                if 'hour' in hourly_data.columns:
                    x_data = hourly_data[hourly_data[metric].notna()]['hour']
                else:
                    x_data = range(len(data_to_plot))
                
                ax.plot(x_data, data_to_plot, color=colors[i], alpha=0.7, linewidth=1)
                ax.set_title(titles[i] if i < len(titles) else f'{metric}随时间变化')
                ax.set_ylabel(metric)
                ax.grid(True, alpha=0.3)
                ax.tick_params(axis='x', rotation=45)
    
    # 第4个子图：综合趋势
    ax = axes_flat[3]
    ax.set_title('综合资源使用趋势')
    
    for i, metric in enumerate(available_metrics):
        data_to_plot = hourly_data[metric].dropna()
        if len(data_to_plot) > 0:
            # 标准化数据到0-100范围以便比较
            max_val = data_to_plot.max()
            if max_val > 0:
                normalized_data = (data_to_plot / max_val) * 100
                
                if 'hour' in hourly_data.columns:
                    x_data = hourly_data[hourly_data[metric].notna()]['hour']
                else:
                    x_data = range(len(normalized_data))
                
                ax.plot(x_data, normalized_data, 
                       label=f'{metric} (normalized)', 
                       color=colors[i], alpha=0.8)
    
    ax.set_ylabel('标准化使用率 (%)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    ax.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    # 绘制每日模式分析
    create_daily_pattern_analysis(ts_data, available_metrics)

def create_daily_pattern_analysis(ts_data, available_metrics):
    """
    创建每日模式分析图
    """
    if 'hour_of_day' not in ts_data.columns:
        return
        
    print("绘制每日模式分析...")
    
    # 按小时聚合
    daily_pattern = ts_data.groupby('hour_of_day').agg({
        metric: 'mean' for metric in available_metrics
    }).reset_index()
    
    fig, axes = plt.subplots(1, len(available_metrics), figsize=(5*len(available_metrics), 4))
    if len(available_metrics) == 1:
        axes = [axes]
    
    fig.suptitle('每日资源使用模式', fontsize=14)
    
    colors = ['blue', 'red', 'green']
    titles = ['CPU使用率日内变化', 'GPU使用率日内变化', '内存使用量日内变化']
    
    for i, metric in enumerate(available_metrics):
        ax = axes[i] if len(available_metrics) > 1 else axes[0]
        
        data = daily_pattern[metric].dropna()
        hours = daily_pattern[daily_pattern[metric].notna()]['hour_of_day']
        
        ax.plot(hours, data, marker='o', color=colors[i], linewidth=2, markersize=4)
        ax.set_title(titles[i] if i < len(titles) else f'{metric}日内变化')
        ax.set_xlabel('小时 (0-23)')
        ax.set_ylabel(metric)
        ax.grid(True, alpha=0.3)
        ax.set_xticks(range(0, 24, 4))
    
    plt.tight_layout()
    plt.show()

def create_statistical_analysis(ts_data, available_metrics):
    """
    创建统计分析图
    """
    print("绘制统计分析...")
    
    fig, axes = plt.subplots(1, len(available_metrics), figsize=(5*len(available_metrics), 4))
    if len(available_metrics) == 1:
        axes = [axes]
    
    fig.suptitle('资源使用分布分析', fontsize=14)
    
    colors = ['blue', 'red', 'green']
    titles = ['CPU使用率分布', 'GPU使用率分布', '内存使用量分布']
    
    for i, metric in enumerate(available_metrics):
        ax = axes[i] if len(available_metrics) > 1 else axes[0]
        
        data = ts_data[metric].dropna()
        if len(data) > 0:
            ax.hist(data, bins=50, alpha=0.7, color=colors[i], edgecolor='black')
            ax.set_title(titles[i] if i < len(titles) else f'{metric}分布')
            ax.set_ylabel('频次')
            ax.set_xlabel(metric)
            ax.grid(True, alpha=0.3)
            
            # 添加统计线
            mean_val = data.mean()
            median_val = data.median()
            ax.axvline(mean_val, color='orange', linestyle='--', 
                      label=f'均值: {mean_val:.2f}')
            ax.axvline(median_val, color='purple', linestyle='--', 
                      label=f'中位数: {median_val:.2f}')
            ax.legend()
    
    plt.tight_layout()
    plt.show()

def main():
    """
    主函数
    """
    print("=== 集群时间序列数据提取和分析 ===\n")
    
    try:
        # 1. 加载和处理数据
        dfa, dfw, dfws = load_and_process_data()
        
        if dfws is None:
            print("数据加载失败，程序退出")
            return
        
        # 2. 提取时间序列数据
        timeseries_data = extract_timeseries_data(dfws)
        
        # 3. 检查可用指标
        metrics = ['cpu_usage', 'gpu_wrk_util', 'avg_mem']
        available_metrics = [m for m in metrics if m in timeseries_data.columns and timeseries_data[m].notna().any()]
        
        print(f"\n可用于分析的指标: {available_metrics}")
        
        if not available_metrics:
            print("没有可用的时间序列指标数据")
            return
        
        # 4. 可视化分析
        visualize_cluster_load(timeseries_data)
        create_statistical_analysis(timeseries_data, available_metrics)
        
        print("\n=== 分析完成 ===")
        print(f"时间序列数据已保存到 clean.csv")
        print(f"数据包含 {len(timeseries_data)} 条记录")
        print(f"时间范围: {timeseries_data['start_date'].min()} 到 {timeseries_data['start_date'].max()}")
        
    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
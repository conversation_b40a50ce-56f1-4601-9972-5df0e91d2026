# Trace data

Due to the recent surge in pulls, this repository has been over the LFS quota. Alternatively, the traces can be downloaded from [Aliyun OSS](https://www.aliyun.com/product/oss) as follows (or running `bash download_data.sh`):

- [pai_group_tag_table.tar.gz](https://aliopentrace.oss-cn-beijing.aliyuncs.com/v2020GPUTraces/pai_group_tag_table.tar.gz)
- [pai_instance_table.tar.gz](https://aliopentrace.oss-cn-beijing.aliyuncs.com/v2020GPUTraces/pai_instance_table.tar.gz)
- [pai_job_table.tar.gz](https://aliopentrace.oss-cn-beijing.aliyuncs.com/v2020GPUTraces/pai_job_table.tar.gz)
- [pai_machine_metric.tar.gz](https://aliopentrace.oss-cn-beijing.aliyuncs.com/v2020GPUTraces/pai_machine_metric.tar.gz)
- [pai_machine_spec.tar.gz](https://aliopentrace.oss-cn-beijing.aliyuncs.com/v2020GPUTraces/pai_machine_spec.tar.gz)
- [pai_sensor_table.tar.gz](https://aliopentrace.oss-cn-beijing.aliyuncs.com/v2020GPUTraces/pai_sensor_table.tar.gz)
- [pai_task_table.tar.gz](https://aliopentrace.oss-cn-beijing.aliyuncs.com/v2020GPUTraces/pai_task_table.tar.gz)

(Backup: [data repo on GitHub](https://github.com/qzweng/clusterdata-cluster-trace-gpu-v2020-data))

Thank you for your support and understanding!

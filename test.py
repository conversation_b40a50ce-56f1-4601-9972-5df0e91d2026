import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib参数
plt.rcParams['font.size'] = 12
plt.rcParams['figure.figsize'] = (10, 8)
plt.rcParams['font.sans-serif'] = ['SimHei']  # 支持中文显示
plt.rcParams['axes.unicode_minus'] = False

def create_individual_feature_plots(dfws):
    """
    为每个特征创建单独的统计图
    """
    print("开始为每个特征生成单独的统计图...")
    
    # 定义要分析的特征
    features = ['cpu_usage', 'gpu_wrk_util', 'avg_mem']
    feature_names = ['CPU使用率', 'GPU工作利用率', '平均内存使用']
    
    # 检查哪些特征在数据中存在
    available_features = []
    available_names = []
    for i, feature in enumerate(features):
        if feature in dfws.columns and dfws[feature].notna().any():
            available_features.append(feature)
            available_names.append(feature_names[i])
    
    if not available_features:
        print("警告: 没有找到可用的资源使用特征")
        return
    
    print(f"可用特征: {available_features}")
    
    # 为每个特征生成单独的统计图
    for i, (feature, name) in enumerate(zip(available_features, available_names)):
        create_single_feature_plot(dfws, feature, name)

def create_single_feature_plot(dfws, feature, feature_name):
    """
    为单个特征创建时间序列统计图
    """
    print(f"正在生成 {feature_name} 的时间序列统计图...")
    
    # 确保时间列存在并转换为datetime
    if 'start_date' in dfws.columns:
        dfws['start_date'] = pd.to_datetime(dfws['start_date'])
        time_col = 'start_date'
    elif 'hour' in dfws.columns:
        dfws['hour'] = pd.to_datetime(dfws['hour'])
        time_col = 'hour'
    else:
        print(f"错误: 没有找到时间列")
        return
    
    # 获取非空数据
    valid_data = dfws[[time_col, feature]].dropna()
    
    if len(valid_data) == 0:
        print(f"警告: {feature} 没有有效数据")
        return
    
    # 创建小时索引用于聚合
    valid_data['hour_index'] = valid_data[time_col].dt.floor('H')
    
    # 按小时聚合数据，计算均值
    hourly_stats = valid_data.groupby('hour_index')[feature].agg(['mean', 'std', 'count', 'min', 'max']).reset_index()
    hourly_stats = hourly_stats.sort_values('hour_index')
    
    print(f"时间范围: {hourly_stats['hour_index'].min()} 到 {hourly_stats['hour_index'].max()}")
    print(f"总小时数: {len(hourly_stats)}")
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(15, 8))
    
    # 绘制时间序列线图
    ax.plot(hourly_stats['hour_index'], hourly_stats['mean'], 
            color='#1f77b4', linewidth=2, label=f'{feature_name}均值', alpha=0.8)
    
    # 添加误差带（标准差）
    if hourly_stats['std'].notna().any():
        std_vals = hourly_stats['std'].fillna(0)
        ax.fill_between(hourly_stats['hour_index'], 
                       hourly_stats['mean'] - std_vals, 
                       hourly_stats['mean'] + std_vals, 
                       color='#1f77b4', alpha=0.3, label='±1标准差')
    
    # 添加最大值和最小值范围
    ax.fill_between(hourly_stats['hour_index'], 
                   hourly_stats['min'], 
                   hourly_stats['max'], 
                   color='lightgray', alpha=0.3, label='最大-最小值范围')
    
    # 设置标题和标签
    ax.set_title(f'{feature_name} 时间序列统计图', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('时间', fontsize=14)
    ax.set_ylabel(f'{feature_name}', fontsize=14)
    ax.legend(fontsize=12)
    ax.grid(True, alpha=0.3)
    
    # 旋转x轴标签以避免重叠
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 添加统计信息文本框
    overall_mean = hourly_stats['mean'].mean()
    overall_std = hourly_stats['mean'].std()
    stats_text = f'''统计信息:
    时间点数: {len(hourly_stats):,}
    整体均值: {overall_mean:.4f}
    整体标准差: {overall_std:.4f}
    最高小时均值: {hourly_stats['mean'].max():.4f}
    最低小时均值: {hourly_stats['mean'].min():.4f}
    变异系数: {(overall_std/overall_mean*100):.2f}%'''
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
            verticalalignment='top', horizontalalignment='left',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存图像
    filename = f'{feature}_timeseries.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"已保存: {filename}")
    plt.show()
    plt.close()

def main():
    """
    主函数 - 只生成三个特征的单独统计图
    """
    print("=== 基于clean.csv的资源使用特征统计分析 ===\n")
    
    try:
        # 读取已处理的数据
        print("正在读取clean.csv...")
        dfws = pd.read_csv('clean.csv')
        print(f"数据加载成功，形状: {dfws.shape}")
        print(f"列名: {list(dfws.columns)}")
        
        # 生成三个特征的单独统计图
        create_individual_feature_plots(dfws)
        
        print("\n=== 分析完成 ===")
        
    except FileNotFoundError:
        print("错误: 未找到clean.csv文件，请先运行main.py生成数据文件")
    except Exception as e:
        print(f"程序执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
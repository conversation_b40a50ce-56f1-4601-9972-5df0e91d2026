# 集群负载资源预测

本项目使用LSTM和ARIMA模型对阿里巴巴集群数据进行负载预测分析。

## 数据集

使用的是阿里巴巴的集群跟踪数据，包含机器资源使用情况、任务执行情况等信息。数据位于`../data/`目录下。

## 功能说明

本项目实现了以下功能：

1. 使用LSTM和ARIMA模型预测集群中的CPU、GPU和内存资源使用情况
2. 对预测结果进行可视化，包括训练历史、拟合结果和未来预测
3. 计算并展示模型的评估指标（MSE、RMSE等）

## 主要文件

- `main.py`: 主要的预测代码，包含数据预处理、模型构建和评估
- `visualization.py`: 额外的可视化代码，生成模型比较图和误差分布图
- `utils.py`: 工具函数，包含数据加载和处理的基础功能

## 依赖项

- Python 3.7+
- pandas
- numpy
- matplotlib
- tensorflow
- statsmodels
- scikit-learn
- seaborn

## 使用方法

1. 确保数据路径正确，默认数据存放在`../data/`目录下
2. 运行预测脚本：

```bash
python main.py
```

3. 运行额外的可视化脚本生成比较图表：

```bash
python visualization.py
```

4. 结果将保存在`../results/`目录中，包括：
   - 预测图表（`lstm_*_prediction.png`和`arima_*_prediction.png`）
   - 性能指标数据（`performance_metrics.csv`）
   - 性能比较图表（`performance_comparison.png`等）

## Windows兼容性说明

本代码已经针对Windows环境进行了优化：

1. 将Linux风格的`/tmp/figures`路径改为更通用的相对路径`../figures/`和`../results/`
2. 添加了异常处理，当无法加载大型数据文件时，会自动使用模拟数据进行演示
3. 确保文件路径使用跨平台兼容的方式创建

## 结果说明

### 预测图表

- LSTM模型图表包含训练损失曲线和预测结果
- ARIMA模型图表展示了训练数据、测试数据的真实值、拟合值以及未来预测

### 评估指标

模型评估使用以下指标：
- MSE (均方误差)
- RMSE (均方根误差)

这些指标被保存在`performance_metrics.csv`文件中，方便比较不同模型和资源的预测性能。 
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集验证脚本
验证生成的clean.csv数据集的质量和完整性
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']
matplotlib.rcParams['axes.unicode_minus'] = False

def validate_dataset(filename='clean.csv'):
    """
    验证数据集的质量
    """
    print("=== 数据集验证报告 ===")
    
    # 读取数据集
    try:
        df = pd.read_csv(filename)
        print(f"✓ 成功读取数据集: {filename}")
        print(f"✓ 数据集大小: {df.shape}")
    except Exception as e:
        print(f"✗ 读取数据集失败: {e}")
        return False
    
    # 检查列名
    expected_columns = ['date', 'hour', 'cpu_usage', 'gpu_wrk_util', 'avg_mem', 'datetime']
    actual_columns = list(df.columns)
    print(f"\n列名检查:")
    print(f"  期望列名: {expected_columns}")
    print(f"  实际列名: {actual_columns}")
    
    missing_columns = set(expected_columns) - set(actual_columns)
    if missing_columns:
        print(f"✗ 缺失列: {missing_columns}")
    else:
        print("✓ 所有期望列都存在")
    
    # 检查数据类型
    print(f"\n数据类型检查:")
    for col in df.columns:
        print(f"  {col}: {df[col].dtype}")
    
    # 检查缺失值
    print(f"\n缺失值检查:")
    missing_counts = df.isnull().sum()
    total_missing = missing_counts.sum()
    if total_missing == 0:
        print("✓ 无缺失值")
    else:
        print(f"✗ 发现 {total_missing} 个缺失值:")
        for col, count in missing_counts.items():
            if count > 0:
                print(f"  {col}: {count} ({count/len(df)*100:.2f}%)")
    
    # 检查数据范围
    print(f"\n数据范围检查:")
    
    # CPU使用率检查
    cpu_min, cpu_max = df['cpu_usage'].min(), df['cpu_usage'].max()
    print(f"  CPU使用率: {cpu_min:.2f} - {cpu_max:.2f}")
    if cpu_min < 0:
        print(f"  ✗ CPU使用率存在负值")
    else:
        print(f"  ✓ CPU使用率无负值")
    
    # GPU使用率检查
    gpu_min, gpu_max = df['gpu_wrk_util'].min(), df['gpu_wrk_util'].max()
    print(f"  GPU使用率: {gpu_min:.2f} - {gpu_max:.2f}")
    if gpu_min < 0 or gpu_max > 100:
        print(f"  ✗ GPU使用率超出0-100%范围")
    else:
        print(f"  ✓ GPU使用率在合理范围内")
    
    # 内存使用检查
    mem_min, mem_max = df['avg_mem'].min(), df['avg_mem'].max()
    print(f"  内存使用: {mem_min:.2f} - {mem_max:.2f} GB")
    if mem_min < 0:
        print(f"  ✗ 内存使用存在负值")
    else:
        print(f"  ✓ 内存使用无负值")
    
    # 小时检查
    hour_min, hour_max = df['hour'].min(), df['hour'].max()
    print(f"  小时: {hour_min} - {hour_max}")
    if hour_min < 0 or hour_max > 23:
        print(f"  ✗ 小时超出0-23范围")
    else:
        print(f"  ✓ 小时在合理范围内")
    
    # 时间连续性检查
    print(f"\n时间连续性检查:")
    df['datetime'] = pd.to_datetime(df['datetime'])
    time_range = df['datetime'].max() - df['datetime'].min()
    print(f"  时间跨度: {time_range}")
    print(f"  最早时间: {df['datetime'].min()}")
    print(f"  最晚时间: {df['datetime'].max()}")
    
    # 数据分布检查
    print(f"\n数据分布摘要:")
    print(df[['cpu_usage', 'gpu_wrk_util', 'avg_mem']].describe())
    
    # 生成简单的验证图表
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('数据集验证图表', fontsize=16)
    
    # 时间分布
    axes[0, 0].hist(df['hour'], bins=24, alpha=0.7, edgecolor='black')
    axes[0, 0].set_title('小时分布')
    axes[0, 0].set_xlabel('小时')
    axes[0, 0].set_ylabel('频次')
    axes[0, 0].grid(True, alpha=0.3)
    
    # CPU使用率分布
    axes[0, 1].hist(df['cpu_usage'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 1].set_title('CPU使用率分布')
    axes[0, 1].set_xlabel('CPU使用率')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].grid(True, alpha=0.3)
    
    # GPU使用率分布
    axes[1, 0].hist(df['gpu_wrk_util'], bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[1, 0].set_title('GPU使用率分布')
    axes[1, 0].set_xlabel('GPU使用率 (%)')
    axes[1, 0].set_ylabel('频次')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 内存使用分布
    axes[1, 1].hist(df['avg_mem'], bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[1, 1].set_title('内存使用分布')
    axes[1, 1].set_xlabel('内存使用 (GB)')
    axes[1, 1].set_ylabel('频次')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('figures/dataset_validation.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"\n=== 验证完成 ===")
    print(f"验证图表已保存到: figures/dataset_validation.png")
    
    return True

def show_sample_data(filename='clean.csv', n=10):
    """
    显示数据集样本
    """
    print(f"\n=== 数据集样本 (前{n}行) ===")
    df = pd.read_csv(filename)
    print(df.head(n).to_string(index=False))
    
    print(f"\n=== 数据集样本 (后{n}行) ===")
    print(df.tail(n).to_string(index=False))

if __name__ == "__main__":
    # 验证数据集
    validate_dataset()
    
    # 显示样本数据
    show_sample_data()

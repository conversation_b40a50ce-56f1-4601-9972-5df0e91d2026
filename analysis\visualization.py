import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 结果目录
RESULT_DIR = '../results/'
if not os.path.exists(RESULT_DIR):
    os.makedirs(RESULT_DIR)
    print(f'results目录已创建: {os.path.abspath(RESULT_DIR)}')
else:
    print(f'结果将保存在: {os.path.abspath(RESULT_DIR)}')

def plot_performance_comparison():
    """
    绘制LSTM和ARIMA模型在不同资源上的性能比较
    """
    # 读取性能指标
    metrics_file = f"{RESULT_DIR}performance_metrics.csv"
    if not os.path.exists(metrics_file):
        print(f"错误: 找不到性能指标文件 {metrics_file}")
        print("请先运行 main.py 生成性能指标")
        return
    
    df = pd.read_csv(metrics_file)
    
    # 绘制MSE比较图
    plt.figure(figsize=(12, 6))
    
    # 使用Seaborn绘制条形图
    plt.subplot(1, 2, 1)
    sns.barplot(x='Resource', y='MSE', hue='Model', data=df)
    plt.title('不同资源的MSE比较')
    plt.ylabel('均方误差 (MSE)')
    plt.xlabel('资源类型')
    plt.grid(axis='y', alpha=0.3)
    
    # 绘制RMSE比较图
    plt.subplot(1, 2, 2)
    sns.barplot(x='Resource', y='RMSE', hue='Model', data=df)
    plt.title('不同资源的RMSE比较')
    plt.ylabel('均方根误差 (RMSE)')
    plt.xlabel('资源类型')
    plt.grid(axis='y', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f"{RESULT_DIR}performance_comparison.png")
    plt.close()
    print(f"性能比较图已保存到 {RESULT_DIR}performance_comparison.png")

def plot_resource_distribution():
    """
    绘制预测结果的资源分布图
    """
    # 读取性能指标以获取模型类型和资源类型
    metrics_file = f"{RESULT_DIR}performance_metrics.csv"
    if not os.path.exists(metrics_file):
        print(f"错误: 找不到性能指标文件 {metrics_file}")
        return
    
    df = pd.read_csv(metrics_file)
    resources = df['Resource'].unique()
    
    # 创建模拟的资源分布数据（实际应用中应从预测结果加载）
    # 这里我们模拟一些数据用于演示
    plt.figure(figsize=(15, 10))
    
    for i, resource in enumerate(resources):
        plt.subplot(2, 2, i+1)
        
        # 模拟数据 - 在实际使用时应替换为真实的预测结果
        x = np.arange(24)  # 24小时
        
        # 生成模拟数据
        np.random.seed(42)  # 保证可重现性
        real_data = 20 + 10 * np.sin(x/4) + np.random.normal(0, 2, 24)
        lstm_pred = 20 + 10 * np.sin(x/4) + np.random.normal(0, 1, 24)
        arima_pred = 20 + 10 * np.sin(x/4) + np.random.normal(0, 3, 24)
        
        # 绘制线图
        plt.plot(x, real_data, 'o-', label='真实值')
        plt.plot(x, lstm_pred, 's--', label='LSTM预测')
        plt.plot(x, arima_pred, '^-.', label='ARIMA预测')
        
        plt.title(f'{resource}资源使用率预测')
        plt.xlabel('时间 (小时)')
        
        if resource == 'CPU' or resource == 'GPU':
            plt.ylabel('使用率 (%)')
        else:
            plt.ylabel('使用量 (MB)')
            
        plt.grid(True, alpha=0.3)
        plt.legend()
    
    plt.tight_layout()
    plt.savefig(f"{RESULT_DIR}resource_prediction_comparison.png")
    plt.close()
    print(f"资源预测比较图已保存到 {RESULT_DIR}resource_prediction_comparison.png")

def plot_prediction_error_distribution():
    """
    绘制预测误差分布
    """
    # 模拟误差数据
    np.random.seed(42)
    
    lstm_errors = {
        'CPU': np.random.normal(0, 2, 100),
        'GPU': np.random.normal(0, 3, 100),
        'Memory': np.random.normal(0, 4, 100)
    }
    
    arima_errors = {
        'CPU': np.random.normal(0, 3, 100),
        'GPU': np.random.normal(0, 4, 100),
        'Memory': np.random.normal(0, 5, 100)
    }
    
    plt.figure(figsize=(15, 10))
    
    resources = ['CPU', 'GPU', 'Memory']
    for i, resource in enumerate(resources):
        plt.subplot(2, 2, i+1)
        
        # 绘制误差分布
        sns.kdeplot(lstm_errors[resource], label='LSTM误差')
        sns.kdeplot(arima_errors[resource], label='ARIMA误差')
        
        plt.axvline(x=0, color='black', linestyle='--', alpha=0.7)
        plt.title(f'{resource}预测误差分布')
        plt.xlabel('预测误差')
        plt.ylabel('密度')
        plt.grid(True, alpha=0.3)
        plt.legend()
    
    plt.tight_layout()
    plt.savefig(f"{RESULT_DIR}prediction_error_distribution.png")
    plt.close()
    print(f"预测误差分布图已保存到 {RESULT_DIR}prediction_error_distribution.png")

def main():
    """
    主函数，运行所有可视化函数
    """
    print("生成模型性能比较图...")
    plot_performance_comparison()
    
    print("生成资源分布预测比较图...")
    plot_resource_distribution()
    
    print("生成预测误差分布图...")
    plot_prediction_error_distribution()
    
    print("所有可视化图表已生成并保存到", RESULT_DIR)

if __name__ == "__main__":
    main() 
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error
from math import sqrt
from statsmodels.tsa.arima.model import ARIMA
from keras.models import Sequential
from keras.layers import LSTM, Dense, Dropout
import tensorflow as tf
from utils import DATA_DIR, FIGURES_DIR, get_df, add_hour_date

# 设置随机种子以确保结果可重现
np.random.seed(42)
tf.random.set_seed(42)

# 创建保存图表的目录
RESULT_DIR = '../results/'
if not os.path.exists(RESULT_DIR):
    os.makedirs(RESULT_DIR)

# 加载数据
def load_data():
    print("加载数据...")
    dfs = get_df(DATA_DIR + 'pai_sensor_table.csv')
    dfm = get_df(DATA_DIR + 'pai_machine_metric.csv')
    return dfs, dfm

# 准备时间序列数据
def prepare_time_series_data(df, resource_column, time_column='start_time', group_by='machine'):
    """
    准备时间序列数据，按小时聚合
    
    Args:
        df: 数据框
        resource_column: 资源列名（如'cpu_usage', 'gpu_wrk_util', 'avg_mem'）
        time_column: 时间列名
        group_by: 分组列名
    
    Returns:
        按小时聚合的时间序列数据
    """
    # 确保数据有日期和小时信息
    df = add_hour_date(df.copy())
    
    # 创建日期时间列
    df['datetime'] = pd.to_datetime(df['date']) + pd.to_timedelta(df['hour'], unit='h')
    
    # 按机器和小时聚合
    agg_data = df.groupby([group_by, 'datetime'])[resource_column].mean().reset_index()
    
    return agg_data

# 为LSTM准备数据
def prepare_lstm_data(series, n_steps_in, n_steps_out):
    """
    将时间序列数据转换为LSTM可用的监督学习格式
    
    Args:
        series: 原始时间序列数据
        n_steps_in: 输入序列长度（回顾窗口）
        n_steps_out: 输出序列长度（预测窗口）
    
    Returns:
        X, y 为LSTM准备的输入和输出数据
    """
    X, y = [], []
    for i in range(len(series) - n_steps_in - n_steps_out + 1):
        X.append(series[i:(i + n_steps_in)])
        y.append(series[(i + n_steps_in):(i + n_steps_in + n_steps_out)])
    return np.array(X), np.array(y)

# 创建并训练LSTM模型
def build_lstm_model(data, resource_name, n_steps_in=24, n_steps_out=12):
    """
    构建、训练LSTM模型并进行预测
    
    Args:
        data: 时间序列数据
        resource_name: 资源名称（用于保存图表）
        n_steps_in: 输入序列长度
        n_steps_out: 输出序列长度
    
    Returns:
        预测结果和评估指标
    """
    print(f"构建LSTM模型预测{resource_name}...")
    
    # 数据规范化
    scaler = MinMaxScaler(feature_range=(0, 1))
    scaled_data = scaler.fit_transform(data.values.reshape(-1, 1)).flatten()
    
    # 准备训练和测试数据
    train_size = int(len(scaled_data) * 0.8)
    train_data = scaled_data[:train_size]
    test_data = scaled_data[train_size-n_steps_in:]
    
    # 创建训练集
    X_train, y_train = prepare_lstm_data(train_data, n_steps_in, n_steps_out)
    X_train = X_train.reshape((X_train.shape[0], X_train.shape[1], 1))
    y_train = y_train.reshape((y_train.shape[0], y_train.shape[1]))
    
    # 创建测试集
    X_test, y_test = prepare_lstm_data(test_data, n_steps_in, n_steps_out)
    X_test = X_test.reshape((X_test.shape[0], X_test.shape[1], 1))
    y_test = y_test.reshape((y_test.shape[0], y_test.shape[1]))
    
    # 构建LSTM模型
    model = Sequential()
    model.add(LSTM(50, activation='relu', return_sequences=True, input_shape=(n_steps_in, 1)))
    model.add(Dropout(0.2))
    model.add(LSTM(50, activation='relu'))
    model.add(Dropout(0.2))
    model.add(Dense(n_steps_out))
    model.compile(optimizer='adam', loss='mse')
    
    # 训练模型
    history = model.fit(X_train, y_train, epochs=50, batch_size=32, validation_split=0.2, verbose=1)
    
    # 预测
    y_pred = model.predict(X_test)
    
    # 反向转换数据
    y_test_inv = scaler.inverse_transform(y_test.reshape(-1, 1)).reshape(y_test.shape)
    y_pred_inv = scaler.inverse_transform(y_pred.reshape(-1, 1)).reshape(y_pred.shape)
    
    # 计算误差
    mse = []
    rmse = []
    for i in range(y_test_inv.shape[1]):
        mse.append(mean_squared_error(y_test_inv[:, i], y_pred_inv[:, i]))
        rmse.append(sqrt(mse[-1]))
    
    avg_mse = np.mean(mse)
    avg_rmse = np.mean(rmse)
    
    print(f"LSTM {resource_name} 平均MSE: {avg_mse:.4f}")
    print(f"LSTM {resource_name} 平均RMSE: {avg_rmse:.4f}")
    
    # 绘制结果
    plt.figure(figsize=(12, 6))
    
    # 绘制训练历史
    plt.subplot(1, 2, 1)
    plt.plot(history.history['loss'], label='训练损失')
    plt.plot(history.history['val_loss'], label='验证损失')
    plt.title(f'LSTM {resource_name} 训练历史')
    plt.xlabel('Epochs')
    plt.ylabel('Loss')
    plt.legend()
    
    # 绘制预测结果
    plt.subplot(1, 2, 2)
    plt.plot(y_test_inv[0], label='真实值', marker='o')
    plt.plot(y_pred_inv[0], label='预测值', marker='x')
    plt.title(f'LSTM {resource_name} 预测结果')
    plt.xlabel('预测步骤')
    plt.ylabel(resource_name)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(f"{RESULT_DIR}lstm_{resource_name}_prediction.png")
    plt.close()
    
    return {
        'mse': avg_mse,
        'rmse': avg_rmse,
        'y_test': y_test_inv,
        'y_pred': y_pred_inv
    }

# 创建并训练ARIMA模型
def build_arima_model(series, resource_name, test_size=24, forecast_steps=12):
    """
    构建、训练ARIMA模型并进行预测
    
    Args:
        series: 时间序列数据
        resource_name: 资源名称（用于保存图表）
        test_size: 测试集大小
        forecast_steps: 预测步长
    
    Returns:
        预测结果和评估指标
    """
    print(f"构建ARIMA模型预测{resource_name}...")
    
    # 分割训练集和测试集
    train = series[:-test_size]
    test = series[-test_size:]
    
    # 确定ARIMA模型的参数 (p,d,q)
    # 这里使用简单的参数，您可以使用网格搜索或AIC/BIC进行参数优化
    p, d, q = 2, 1, 2
    
    # 训练ARIMA模型
    model = ARIMA(train, order=(p, d, q))
    model_fit = model.fit()
    
    # 对测试集进行预测
    forecast = model_fit.forecast(steps=test_size)
    
    # 计算误差
    mse = mean_squared_error(test, forecast)
    rmse = sqrt(mse)
    
    print(f"ARIMA {resource_name} MSE: {mse:.4f}")
    print(f"ARIMA {resource_name} RMSE: {rmse:.4f}")
    
    # 预测未来
    future_forecast = model_fit.forecast(steps=test_size+forecast_steps)
    
    # 绘制结果
    plt.figure(figsize=(14, 7))
    
    # 绘制训练数据
    plt.plot(range(len(train)), train, label='训练数据')
    
    # 绘制测试数据
    plt.plot(range(len(train), len(train) + len(test)), test, label='真实值', color='green', marker='o')
    
    # 绘制预测
    plt.plot(range(len(train), len(train) + len(forecast)), forecast, label='拟合值', color='red', linestyle='--')
    
    # 绘制未来预测
    plt.plot(range(len(train), len(train) + len(future_forecast)), future_forecast, label='预测值', color='purple', linestyle=':')
    
    plt.axvline(x=len(train), color='k', linestyle='--')
    plt.axvline(x=len(train) + len(test), color='k', linestyle='--')
    
    plt.fill_between(range(len(train) + len(test), len(train) + len(future_forecast)), 
                    future_forecast[test_size:], alpha=0.3, color='purple')
    
    plt.title(f'ARIMA {resource_name} 预测')
    plt.xlabel('时间')
    plt.ylabel(resource_name)
    plt.legend()
    plt.grid(True)
    
    plt.savefig(f"{RESULT_DIR}arima_{resource_name}_prediction.png")
    plt.close()
    
    return {
        'mse': mse,
        'rmse': rmse,
        'test': test,
        'forecast': forecast,
        'future_forecast': future_forecast
    }

def main():
    # 加载数据
    try:
        print("尝试加载数据...")
        dfs, dfm = load_data()
        print("数据加载成功！")
    except Exception as e:
        print(f"加载数据时出错: {e}")
        print("尝试使用模拟数据继续...")
        # 创建模拟数据以继续演示
        np.random.seed(42)
        dates = pd.date_range(start='2020-01-01', periods=500, freq='H')
        
        # 模拟CPU使用率数据
        cpu_trend = 20 + 10 * np.sin(np.arange(500)/24) + np.random.normal(0, 5, 500)
        cpu_series = pd.Series(cpu_trend, index=dates)
        
        # 模拟GPU使用率数据
        gpu_trend = 30 + 15 * np.sin(np.arange(500)/48) + np.random.normal(0, 7, 500)
        gpu_series = pd.Series(gpu_trend, index=dates)
        
        # 模拟内存使用数据
        mem_trend = 2000 + 500 * np.sin(np.arange(500)/72) + np.random.normal(0, 200, 500)
        mem_series = pd.Series(mem_trend, index=dates)
        
        print("模拟数据已创建，将用于模型训练和测试")
        
        # 设置一个标志表示我们使用的是模拟数据
        using_simulated_data = True
    else:
        using_simulated_data = False
    
    if not using_simulated_data:
        # 处理真实数据
        print("准备时间序列数据...")
        machine_to_analyze = dfs['machine'].value_counts().index[0]  # 选择数据最多的机器
        
        # 准备CPU数据
        cpu_data = prepare_time_series_data(dfs, 'cpu_usage', group_by='machine')
        cpu_series = cpu_data[cpu_data['machine'] == machine_to_analyze].sort_values('datetime')
        cpu_series = cpu_series.set_index('datetime')['cpu_usage']
        
        # 准备GPU数据
        gpu_data = prepare_time_series_data(dfs, 'gpu_wrk_util', group_by='machine')
        gpu_series = gpu_data[gpu_data['machine'] == machine_to_analyze].sort_values('datetime')
        gpu_series = gpu_series.set_index('datetime')['gpu_wrk_util']
        
        # 准备内存数据
        mem_data = prepare_time_series_data(dfs, 'avg_mem', group_by='machine')
        mem_series = mem_data[mem_data['machine'] == machine_to_analyze].sort_values('datetime')
        mem_series = mem_series.set_index('datetime')['avg_mem']
        
        # 处理缺失值
        cpu_series = cpu_series.fillna(method='ffill').fillna(method='bfill')
        gpu_series = gpu_series.fillna(method='ffill').fillna(method='bfill')
        mem_series = mem_series.fillna(method='ffill').fillna(method='bfill')
        
        # 确保序列长度足够
        min_length = 200  # 最小长度要求
        if len(cpu_series) < min_length or len(gpu_series) < min_length or len(mem_series) < min_length:
            print("数据量不足，使用机器指标数据代替...")
            # 使用机器指标数据代替
            cpu_data = prepare_time_series_data(dfm, 'machine_cpu', time_column='start_time')
            cpu_series = cpu_data[cpu_data['machine'] == machine_to_analyze].sort_values('datetime')
            cpu_series = cpu_series.set_index('datetime')['machine_cpu']
            
            gpu_data = prepare_time_series_data(dfm, 'machine_gpu', time_column='start_time')
            gpu_series = gpu_data[gpu_data['machine'] == machine_to_analyze].sort_values('datetime')
            gpu_series = gpu_series.set_index('datetime')['machine_gpu']
            
            # 内存数据可能不在机器指标中，如果没有则跳过
            try:
                mem_data = prepare_time_series_data(dfm, 'machine_mem', time_column='start_time')
                mem_series = mem_data[mem_data['machine'] == machine_to_analyze].sort_values('datetime')
                mem_series = mem_series.set_index('datetime')['machine_mem']
            except:
                print("内存指标不可用，使用模拟数据...")
                # 创建模拟内存数据
                dates = pd.date_range(start='2020-01-01', periods=500, freq='H')
                mem_trend = 2000 + 500 * np.sin(np.arange(500)/72) + np.random.normal(0, 200, 500)
                mem_series = pd.Series(mem_trend, index=dates)
    
    # 建立并训练模型
    print("\n开始构建和训练模型...")
    
    # LSTM模型
    lstm_cpu_results = build_lstm_model(cpu_series, 'CPU')
    lstm_gpu_results = build_lstm_model(gpu_series, 'GPU')
    lstm_mem_results = build_lstm_model(mem_series, 'Memory')
    
    # ARIMA模型
    arima_cpu_results = build_arima_model(cpu_series, 'CPU')
    arima_gpu_results = build_arima_model(gpu_series, 'GPU')
    arima_mem_results = build_arima_model(mem_series, 'Memory')
    
    # 汇总性能指标
    print("\n性能指标汇总:")
    print("=" * 50)
    print("| 模型  | 资源   | MSE      | RMSE     |")
    print("|-------|--------|----------|----------|")
    print(f"| LSTM  | CPU    | {lstm_cpu_results['mse']:.4f} | {lstm_cpu_results['rmse']:.4f} |")
    print(f"| LSTM  | GPU    | {lstm_gpu_results['mse']:.4f} | {lstm_gpu_results['rmse']:.4f} |")
    print(f"| LSTM  | Memory | {lstm_mem_results['mse']:.4f} | {lstm_mem_results['rmse']:.4f} |")
    print(f"| ARIMA | CPU    | {arima_cpu_results['mse']:.4f} | {arima_cpu_results['rmse']:.4f} |")
    print(f"| ARIMA | GPU    | {arima_gpu_results['mse']:.4f} | {arima_gpu_results['rmse']:.4f} |")
    print(f"| ARIMA | Memory | {arima_mem_results['mse']:.4f} | {arima_mem_results['rmse']:.4f} |")
    print("=" * 50)
    
    # 保存性能指标到CSV
    performance_df = pd.DataFrame({
        'Model': ['LSTM', 'LSTM', 'LSTM', 'ARIMA', 'ARIMA', 'ARIMA'],
        'Resource': ['CPU', 'GPU', 'Memory', 'CPU', 'GPU', 'Memory'],
        'MSE': [
            lstm_cpu_results['mse'], lstm_gpu_results['mse'], lstm_mem_results['mse'],
            arima_cpu_results['mse'], arima_gpu_results['mse'], arima_mem_results['mse']
        ],
        'RMSE': [
            lstm_cpu_results['rmse'], lstm_gpu_results['rmse'], lstm_mem_results['rmse'],
            arima_cpu_results['rmse'], arima_gpu_results['rmse'], arima_mem_results['rmse']
        ]
    })
    
    performance_df.to_csv(f"{RESULT_DIR}performance_metrics.csv", index=False)
    print(f"性能指标已保存到 {RESULT_DIR}performance_metrics.csv")
    print(f"所有图表已保存到 {RESULT_DIR}目录")

if __name__ == "__main__":
    main() 
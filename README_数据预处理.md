# PAI集群数据预处理项目

## 项目概述

本项目完成了对阿里巴巴PAI集群GPU追踪数据的预处理，生成了基于时间序列的清洁数据集，用于后续的机器学习实验。

## 主要功能

### 1. 数据提取和合并
- 使用 `analysis/utils.py` 中的相关函数加载PAI集群的7个数据表
- 重点提取 `pai_sensor_table` 和 `pai_instance_table` 中的关键信息
- 合并时间信息和资源使用指标

### 2. 缺失值处理
- **检测缺失值**: 自动统计各字段的缺失情况
- **智能填充**: 
  - 删除所有指标都缺失的记录
  - 使用同一时间段的平均值填充部分缺失值
  - 使用全局中位数填充剩余缺失值
- **处理结果**: CPU使用率缺失0.06%，内存使用缺失0.02%，GPU使用率无缺失

### 3. 时间序列数据集构建
- **时间聚合**: 按日期和小时聚合数据，计算每小时的平均资源使用情况
- **字段包含**: 
  - `date`: 日期
  - `hour`: 小时 (0-23)
  - `cpu_usage`: CPU使用率
  - `gpu_wrk_util`: GPU使用率 (%)
  - `avg_mem`: 平均内存使用 (GB)
  - `datetime`: 完整的日期时间

### 4. 数据清洗和异常值处理
- **CPU使用率**: 移除负值和超过10000%的异常值
- **GPU使用率**: 确保在0-100%范围内
- **内存使用**: 移除负值和超过1TB的异常值
- **时间范围**: 1970-01-19 到 1970-03-16，跨度56天

### 5. 可视化分析
- **时间序列图**: 展示CPU、GPU、内存使用的时间变化趋势
- **综合对比图**: 标准化后的三个指标对比
- **验证图表**: 数据分布和质量验证

## 生成的文件

### 核心数据集
- **`clean.csv`**: 清洁的时间序列数据集 (1,255条记录)
  - 包含date, hour, cpu_usage, gpu_wrk_util, avg_mem, datetime字段
  - 无缺失值，已处理异常值
  - 按时间排序，适合时间序列分析

### 统计分析
- **`statistics_summary.csv`**: 基本统计量摘要
  - 包含均值、中位数、标准差、分位数等
- **`analysis_report.md`**: 详细分析报告
  - 正态性检验结果
  - 相关性分析结果

### 可视化图表
- **`figures/time_series_analysis.png`**: 时间序列分析图
- **`figures/resource_comparison.png`**: 资源使用对比图
- **`figures/dataset_validation.png`**: 数据集验证图

## 数据集特征

### 基本统计信息
| 指标 | 均值 | 中位数 | 标准差 | 最小值 | 最大值 |
|------|------|--------|--------|--------|--------|
| CPU使用率 | 339.02 | 303.69 | 142.67 | 1.02 | 1336.18 |
| GPU使用率(%) | 12.26 | 11.06 | 7.60 | 0.00 | 98.05 |
| 内存使用(GB) | 6.75 | 6.15 | 4.32 | 0.09 | 98.23 |

### 数据质量
- ✅ 无缺失值
- ✅ 无异常值
- ✅ 时间连续性良好
- ✅ 数据范围合理

## 使用方法

### 运行数据预处理
```bash
python main.py
```

### 验证数据集质量
```bash
python validate_dataset.py
```

### 高级分析 (可选)
```bash
python data_analysis.py
```

## 代码结构

### 主要文件
- **`main.py`**: 主要的数据预处理程序
- **`data_analysis.py`**: 扩展的数据分析功能
- **`validate_dataset.py`**: 数据集验证脚本

### 核心函数
- `load_and_merge_data()`: 加载和合并原始数据
- `create_time_series_dataset()`: 创建时间序列数据集
- `save_clean_dataset()`: 保存清洁数据集
- `calculate_basic_statistics()`: 计算基本统计量
- `create_time_series_visualizations()`: 生成时间序列可视化

## 技术特点

### 缺失值处理策略
1. **分层处理**: 先删除完全缺失的记录，再处理部分缺失
2. **时间感知**: 使用同一时间段的数据进行填充
3. **稳健性**: 多重备选方案确保数据完整性

### 时间序列优化
1. **时间聚合**: 按小时聚合减少噪声
2. **时间索引**: 创建标准化的datetime字段
3. **排序优化**: 按时间顺序排列便于分析

### 数据验证
1. **自动检测**: 数据类型、范围、缺失值检查
2. **可视化验证**: 分布图和时间序列图
3. **统计验证**: 基本统计量和异常值检测

## 后续使用建议

### 适用场景
- 时间序列预测模型
- 资源使用模式分析
- 集群负载预测
- 异常检测算法

### 注意事项
- 数据已按小时聚合，适合小时级别的分析
- CPU使用率可能超过100%（多核系统）
- 时间戳已脱敏，保持相对时间关系

## 依赖环境

```
pandas >= 1.3.0
numpy >= 1.21.0
matplotlib >= 3.4.0
seaborn >= 0.11.0
scipy >= 1.7.0
```

## 联系信息

如有问题或建议，请参考项目文档或提交issue。
